using System;
using System.Windows.Forms;

namespace AotForms
{
    /// <summary>
    /// نافذة إعدادات التحريك التلقائي نحو العظام
    /// </summary>
    public partial class AutoTeleportConfig : Form
    {
        private CheckBox enableCheckBox;
        private TrackBar speedTrackBar;
        private TrackBar minDistanceTrackBar;
        private TrackBar updateDelayTrackBar;
        private Label speedLabel;
        private Label minDistanceLabel;
        private Label updateDelayLabel;
        private Button startButton;
        private Button stopButton;
        private Label statusLabel;

        public AutoTeleportConfig()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "Auto Teleport to Bone Settings";
            this.Size = new System.Drawing.Size(400, 350);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;

            // Enable/Disable CheckBox
            enableCheckBox = new CheckBox
            {
                Text = "تفعيل التحريك التلقائي",
                Location = new System.Drawing.Point(20, 20),
                Size = new System.Drawing.Size(200, 25),
                Checked = AutoTeleportToBone.IsEnabled
            };
            enableCheckBox.CheckedChanged += EnableCheckBox_CheckedChanged;
            this.Controls.Add(enableCheckBox);

            // Speed Control
            var speedGroupLabel = new Label
            {
                Text = "سرعة التحريك:",
                Location = new System.Drawing.Point(20, 60),
                Size = new System.Drawing.Size(100, 20)
            };
            this.Controls.Add(speedGroupLabel);

            speedTrackBar = new TrackBar
            {
                Location = new System.Drawing.Point(20, 85),
                Size = new System.Drawing.Size(250, 45),
                Minimum = 1,
                Maximum = 100,
                Value = (int)(AutoTeleportToBone.TeleportSpeed * 100),
                TickFrequency = 10
            };
            speedTrackBar.ValueChanged += SpeedTrackBar_ValueChanged;
            this.Controls.Add(speedTrackBar);

            speedLabel = new Label
            {
                Location = new System.Drawing.Point(280, 95),
                Size = new System.Drawing.Size(80, 20),
                Text = $"{AutoTeleportToBone.TeleportSpeed:F2}"
            };
            this.Controls.Add(speedLabel);

            // Min Distance Control
            var minDistanceGroupLabel = new Label
            {
                Text = "المسافة الدنيا للتوقف:",
                Location = new System.Drawing.Point(20, 140),
                Size = new System.Drawing.Size(150, 20)
            };
            this.Controls.Add(minDistanceGroupLabel);

            minDistanceTrackBar = new TrackBar
            {
                Location = new System.Drawing.Point(20, 165),
                Size = new System.Drawing.Size(250, 45),
                Minimum = 1,
                Maximum = 100,
                Value = (int)(AutoTeleportToBone.MinDistanceToTarget * 10),
                TickFrequency = 10
            };
            minDistanceTrackBar.ValueChanged += MinDistanceTrackBar_ValueChanged;
            this.Controls.Add(minDistanceTrackBar);

            minDistanceLabel = new Label
            {
                Location = new System.Drawing.Point(280, 175),
                Size = new System.Drawing.Size(80, 20),
                Text = $"{AutoTeleportToBone.MinDistanceToTarget:F1}m"
            };
            this.Controls.Add(minDistanceLabel);

            // Update Delay Control
            var updateDelayGroupLabel = new Label
            {
                Text = "تأخير التحديث (ms):",
                Location = new System.Drawing.Point(20, 220),
                Size = new System.Drawing.Size(150, 20)
            };
            this.Controls.Add(updateDelayGroupLabel);

            updateDelayTrackBar = new TrackBar
            {
                Location = new System.Drawing.Point(20, 245),
                Size = new System.Drawing.Size(250, 45),
                Minimum = 10,
                Maximum = 500,
                Value = AutoTeleportToBone.UpdateDelayMs,
                TickFrequency = 50
            };
            updateDelayTrackBar.ValueChanged += UpdateDelayTrackBar_ValueChanged;
            this.Controls.Add(updateDelayTrackBar);

            updateDelayLabel = new Label
            {
                Location = new System.Drawing.Point(280, 255),
                Size = new System.Drawing.Size(80, 20),
                Text = $"{AutoTeleportToBone.UpdateDelayMs}ms"
            };
            this.Controls.Add(updateDelayLabel);

            // Control Buttons
            startButton = new Button
            {
                Text = "بدء التشغيل",
                Location = new System.Drawing.Point(20, 300),
                Size = new System.Drawing.Size(80, 30),
                Enabled = !AutoTeleportToBone.IsEnabled
            };
            startButton.Click += StartButton_Click;
            this.Controls.Add(startButton);

            stopButton = new Button
            {
                Text = "إيقاف",
                Location = new System.Drawing.Point(110, 300),
                Size = new System.Drawing.Size(80, 30),
                Enabled = AutoTeleportToBone.IsEnabled
            };
            stopButton.Click += StopButton_Click;
            this.Controls.Add(stopButton);

            // Status Label
            statusLabel = new Label
            {
                Location = new System.Drawing.Point(200, 305),
                Size = new System.Drawing.Size(150, 20),
                Text = AutoTeleportToBone.IsEnabled ? "نشط" : "متوقف"
            };
            this.Controls.Add(statusLabel);
        }

        private void LoadCurrentSettings()
        {
            if (enableCheckBox != null)
                enableCheckBox.Checked = AutoTeleportToBone.IsEnabled;
        }

        private void EnableCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            AutoTeleportToBone.IsEnabled = enableCheckBox.Checked;
            UpdateButtonStates();
        }

        private void SpeedTrackBar_ValueChanged(object sender, EventArgs e)
        {
            AutoTeleportToBone.TeleportSpeed = speedTrackBar.Value / 100.0f;
            speedLabel.Text = $"{AutoTeleportToBone.TeleportSpeed:F2}";
        }

        private void MinDistanceTrackBar_ValueChanged(object sender, EventArgs e)
        {
            AutoTeleportToBone.MinDistanceToTarget = minDistanceTrackBar.Value / 10.0f;
            minDistanceLabel.Text = $"{AutoTeleportToBone.MinDistanceToTarget:F1}m";
        }

        private void UpdateDelayTrackBar_ValueChanged(object sender, EventArgs e)
        {
            AutoTeleportToBone.UpdateDelayMs = updateDelayTrackBar.Value;
            updateDelayLabel.Text = $"{AutoTeleportToBone.UpdateDelayMs}ms";
        }

        private void StartButton_Click(object sender, EventArgs e)
        {
            AutoTeleportToBone.IsEnabled = true;
            AutoTeleportToBone.Start();
            enableCheckBox.Checked = true;
            UpdateButtonStates();
        }

        private void StopButton_Click(object sender, EventArgs e)
        {
            AutoTeleportToBone.Stop();
            enableCheckBox.Checked = false;
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            startButton.Enabled = !AutoTeleportToBone.IsEnabled;
            stopButton.Enabled = AutoTeleportToBone.IsEnabled;
            statusLabel.Text = AutoTeleportToBone.IsEnabled ? "نشط" : "متوقف";
        }
    }

    /// <summary>
    /// كلاس مساعد لإدارة إعدادات التحريك التلقائي
    /// </summary>
    public static class AutoTeleportManager
    {
        private static AutoTeleportConfig configForm;

        /// <summary>
        /// فتح نافذة الإعدادات
        /// </summary>
        public static void ShowConfigWindow()
        {
            if (configForm == null || configForm.IsDisposed)
            {
                configForm = new AutoTeleportConfig();
            }

            if (!configForm.Visible)
            {
                configForm.Show();
            }
            else
            {
                configForm.BringToFront();
            }
        }

        /// <summary>
        /// تفعيل سريع للنظام
        /// </summary>
        public static void QuickStart()
        {
            AutoTeleportToBone.IsEnabled = true;
            AutoTeleportToBone.Start();
        }

        /// <summary>
        /// إيقاف سريع للنظام
        /// </summary>
        public static void QuickStop()
        {
            AutoTeleportToBone.Stop();
        }

        /// <summary>
        /// تبديل حالة النظام (تشغيل/إيقاف)
        /// </summary>
        public static void Toggle()
        {
            if (AutoTeleportToBone.IsEnabled)
            {
                QuickStop();
            }
            else
            {
                QuickStart();
            }
        }
    }
}
