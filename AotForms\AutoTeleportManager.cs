using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AotForms
{
    /// <summary>
    /// مدير نظام التحريك التلقائي نحو العظام
    /// </summary>
    public static class AutoTeleportManager
    {
        private static Form configWindow;
        private static bool isConfigWindowOpen = false;

        /// <summary>
        /// تبديل حالة النظام (تشغيل/إيقاف)
        /// </summary>
        public static void Toggle()
        {
            Config.AutoTeleportToBone = !Config.AutoTeleportToBone;
            Config.Notif();
        }

        /// <summary>
        /// تشغيل سريع للنظام
        /// </summary>
        public static void QuickStart()
        {
            Config.AutoTeleportToBone = true;
            AutoTeleportToBone.Start();
            Config.Notif();
        }

        /// <summary>
        /// إيقاف سريع للنظام
        /// </summary>
        public static void QuickStop()
        {
            Config.AutoTeleportToBone = false;
            AutoTeleportToBone.Stop();
            Config.Notif();
        }

        /// <summary>
        /// فتح نافذة الإعدادات المتقدمة
        /// </summary>
        public static void ShowConfigWindow()
        {
            if (isConfigWindowOpen && configWindow != null && !configWindow.IsDisposed)
            {
                configWindow.BringToFront();
                return;
            }

            CreateConfigWindow();
        }

        /// <summary>
        /// إنشاء نافذة الإعدادات
        /// </summary>
        private static void CreateConfigWindow()
        {
            configWindow = new Form
            {
                Text = "Auto Teleport to Bone Settings",
                Size = new Size(400, 350),
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                BackColor = Color.FromArgb(40, 40, 40),
                ForeColor = Color.White
            };

            // Checkbox للتفعيل
            var enableCheckBox = new CheckBox
            {
                Text = "تفعيل التحريك التلقائي",
                Location = new Point(20, 20),
                Size = new Size(200, 25),
                Checked = Config.AutoTeleportToBone,
                ForeColor = Color.White
            };
            enableCheckBox.CheckedChanged += (s, e) => 
            {
                Config.AutoTeleportToBone = enableCheckBox.Checked;
                Config.Notif();
            };

            // سرعة التحريك
            var speedLabel = new Label
            {
                Text = $"سرعة التحريك: {Config.AutoTeleportSpeed:F2}",
                Location = new Point(20, 60),
                Size = new Size(200, 20),
                ForeColor = Color.White
            };

            var speedTrackBar = new TrackBar
            {
                Location = new Point(20, 85),
                Size = new Size(300, 45),
                Minimum = 1,
                Maximum = 100,
                Value = (int)(Config.AutoTeleportSpeed * 100),
                TickFrequency = 10
            };
            speedTrackBar.ValueChanged += (s, e) =>
            {
                Config.AutoTeleportSpeed = speedTrackBar.Value / 100f;
                speedLabel.Text = $"سرعة التحريك: {Config.AutoTeleportSpeed:F2}";
            };

            // المسافة الدنيا
            var distanceLabel = new Label
            {
                Text = $"المسافة الدنيا للتوقف: {Config.AutoTeleportMinDistance:F1}m",
                Location = new Point(20, 140),
                Size = new Size(250, 20),
                ForeColor = Color.White
            };

            var distanceTrackBar = new TrackBar
            {
                Location = new Point(20, 165),
                Size = new Size(300, 45),
                Minimum = 1,
                Maximum = 100,
                Value = (int)(Config.AutoTeleportMinDistance * 10),
                TickFrequency = 10
            };
            distanceTrackBar.ValueChanged += (s, e) =>
            {
                Config.AutoTeleportMinDistance = distanceTrackBar.Value / 10f;
                distanceLabel.Text = $"المسافة الدنيا للتوقف: {Config.AutoTeleportMinDistance:F1}m";
            };

            // تأخير التحديث
            var delayLabel = new Label
            {
                Text = $"تأخير التحديث: {Config.AutoTeleportUpdateDelay}ms",
                Location = new Point(20, 220),
                Size = new Size(200, 20),
                ForeColor = Color.White
            };

            var delayTrackBar = new TrackBar
            {
                Location = new Point(20, 245),
                Size = new Size(300, 45),
                Minimum = 10,
                Maximum = 500,
                Value = Config.AutoTeleportUpdateDelay,
                TickFrequency = 50
            };
            delayTrackBar.ValueChanged += (s, e) =>
            {
                Config.AutoTeleportUpdateDelay = delayTrackBar.Value;
                delayLabel.Text = $"تأخير التحديث: {Config.AutoTeleportUpdateDelay}ms";
            };

            // أزرار التحكم
            var startButton = new Button
            {
                Text = "بدء التشغيل",
                Location = new Point(20, 300),
                Size = new Size(100, 30),
                BackColor = Color.Green,
                ForeColor = Color.White
            };
            startButton.Click += (s, e) => QuickStart();

            var stopButton = new Button
            {
                Text = "إيقاف",
                Location = new Point(130, 300),
                Size = new Size(100, 30),
                BackColor = Color.Red,
                ForeColor = Color.White
            };
            stopButton.Click += (s, e) => QuickStop();

            // إضافة العناصر للنافذة
            configWindow.Controls.AddRange(new Control[]
            {
                enableCheckBox,
                speedLabel, speedTrackBar,
                distanceLabel, distanceTrackBar,
                delayLabel, delayTrackBar,
                startButton, stopButton
            });

            configWindow.FormClosed += (s, e) => isConfigWindowOpen = false;
            isConfigWindowOpen = true;
            configWindow.Show();
        }
    }
}
