using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AotForms
{
    /// <summary>
    /// مثال على كيفية استخدام نظام التحريك التلقائي نحو العظام
    /// </summary>
    public static class AutoTeleportExample
    {
        /// <summary>
        /// مثال بسيط لتشغيل النظام
        /// </summary>
        public static void SimpleExample()
        {
            // تعيين الإعدادات
            AutoTeleportToBone.TeleportSpeed = 0.05f; // حركة بطيئة وسلسة
            AutoTeleportToBone.MinDistanceToTarget = 3.0f; // توقف عند 3 متر
            AutoTeleportToBone.UpdateDelayMs = 50; // تحديث كل 50ms
            
            // تفعيل النظام
            AutoTeleportToBone.IsEnabled = true;
            AutoTeleportToBone.Start();
            
            Console.WriteLine("تم تشغيل نظام التحريك التلقائي نحو العظام");
        }

        /// <summary>
        /// مثال متقدم مع إعدادات مخصصة
        /// </summary>
        public static void AdvancedExample()
        {
            // إعدادات للحركة السريعة والعدوانية
            AutoTeleportToBone.TeleportSpeed = 0.2f; // حركة سريعة
            AutoTeleportToBone.MinDistanceToTarget = 1.5f; // اقتراب أكثر
            AutoTeleportToBone.UpdateDelayMs = 25; // تحديث أسرع
            
            // تشغيل النظام
            AutoTeleportToBone.IsEnabled = true;
            AutoTeleportToBone.Start();
            
            Console.WriteLine("تم تشغيل النظام بإعدادات متقدمة");
        }

        /// <summary>
        /// مثال على التحكم بالنظام عبر مفاتيح الكيبورد
        /// </summary>
        public static void KeyboardControlExample()
        {
            // يمكن ربط هذه الدوال بمفاتيح معينة
            
            // مفتاح F1 - تشغيل/إيقاف
            // AutoTeleportManager.Toggle();
            
            // مفتاح F2 - فتح نافذة الإعدادات
            // AutoTeleportManager.ShowConfigWindow();
            
            // مفتاح F3 - تشغيل سريع
            // AutoTeleportManager.QuickStart();
            
            // مفتاح F4 - إيقاف سريع
            // AutoTeleportManager.QuickStop();
        }

        /// <summary>
        /// مثال على مراقبة حالة النظام
        /// </summary>
        public static async Task MonitoringExample()
        {
            while (true)
            {
                if (AutoTeleportToBone.IsEnabled)
                {
                    Console.WriteLine($"النظام نشط - السرعة: {AutoTeleportToBone.TeleportSpeed:F2}");
                }
                else
                {
                    Console.WriteLine("النظام متوقف");
                }
                
                await Task.Delay(5000); // تحقق كل 5 ثوان
            }
        }

        /// <summary>
        /// مثال على إعدادات مختلفة حسب الحالة
        /// </summary>
        public static void DynamicSettingsExample()
        {
            // إعدادات للوضع الدفاعي (حركة بطيئة وحذرة)
            SetDefensiveMode();
            
            // إعدادات للوضع الهجومي (حركة سريعة وعدوانية)
            // SetAggressiveMode();
            
            // إعدادات للوضع المتوازن
            // SetBalancedMode();
        }

        private static void SetDefensiveMode()
        {
            AutoTeleportToBone.TeleportSpeed = 0.03f; // حركة بطيئة جداً
            AutoTeleportToBone.MinDistanceToTarget = 5.0f; // مسافة آمنة
            AutoTeleportToBone.UpdateDelayMs = 100; // تحديث بطيء
            
            Console.WriteLine("تم تفعيل الوضع الدفاعي");
        }

        private static void SetAggressiveMode()
        {
            AutoTeleportToBone.TeleportSpeed = 0.3f; // حركة سريعة
            AutoTeleportToBone.MinDistanceToTarget = 1.0f; // اقتراب شديد
            AutoTeleportToBone.UpdateDelayMs = 20; // تحديث سريع جداً
            
            Console.WriteLine("تم تفعيل الوضع الهجومي");
        }

        private static void SetBalancedMode()
        {
            AutoTeleportToBone.TeleportSpeed = 0.1f; // حركة متوسطة
            AutoTeleportToBone.MinDistanceToTarget = 2.5f; // مسافة متوسطة
            AutoTeleportToBone.UpdateDelayMs = 50; // تحديث متوسط
            
            Console.WriteLine("تم تفعيل الوضع المتوازن");
        }

        /// <summary>
        /// مثال على إيقاف النظام بأمان
        /// </summary>
        public static void SafeShutdownExample()
        {
            try
            {
                // إيقاف النظام
                AutoTeleportToBone.Stop();
                
                // انتظار قليل للتأكد من الإيقاف
                Task.Delay(500).Wait();
                
                Console.WriteLine("تم إيقاف النظام بأمان");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ أثناء إيقاف النظام: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// كلاس للتحكم في النظام عبر واجهة المستخدم
    /// </summary>
    public static class AutoTeleportUI
    {
        /// <summary>
        /// إضافة أزرار التحكم إلى نموذج موجود
        /// </summary>
        public static void AddControlsToForm(Form parentForm)
        {
            // زر التشغيل/الإيقاف
            var toggleButton = new Button
            {
                Text = "تبديل التحريك التلقائي",
                Location = new System.Drawing.Point(10, 10),
                Size = new System.Drawing.Size(150, 30)
            };
            toggleButton.Click += (s, e) => AutoTeleportManager.Toggle();
            parentForm.Controls.Add(toggleButton);

            // زر الإعدادات
            var settingsButton = new Button
            {
                Text = "إعدادات التحريك",
                Location = new System.Drawing.Point(170, 10),
                Size = new System.Drawing.Size(120, 30)
            };
            settingsButton.Click += (s, e) => AutoTeleportManager.ShowConfigWindow();
            parentForm.Controls.Add(settingsButton);

            // تسمية الحالة
            var statusLabel = new Label
            {
                Text = "الحالة: متوقف",
                Location = new System.Drawing.Point(10, 50),
                Size = new System.Drawing.Size(200, 20)
            };
            parentForm.Controls.Add(statusLabel);

            // تحديث تسمية الحالة كل ثانية
            var timer = new Timer { Interval = 1000 };
            timer.Tick += (s, e) => 
            {
                statusLabel.Text = AutoTeleportToBone.IsEnabled ? "الحالة: نشط" : "الحالة: متوقف";
            };
            timer.Start();
        }

        /// <summary>
        /// إنشاء نافذة تحكم مستقلة
        /// </summary>
        public static Form CreateControlWindow()
        {
            var form = new Form
            {
                Text = "التحكم في التحريك التلقائي",
                Size = new System.Drawing.Size(350, 150),
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                StartPosition = FormStartPosition.CenterScreen
            };

            AddControlsToForm(form);
            return form;
        }
    }
}
