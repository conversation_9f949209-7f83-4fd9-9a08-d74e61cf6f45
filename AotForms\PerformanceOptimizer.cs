using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace AotForms
{
    internal static class PerformanceOptimizer
    {
        private static readonly System.Threading.Timer _gcTimer;
        private static readonly System.Threading.Timer _performanceTimer;
        private static long _lastGcMemory = 0;
        private static DateTime _lastGcTime = DateTime.Now;

        //static PerformanceOptimizer()
        //{
        //    // Schedule periodic GC and performance monitoring
        //    _gcTimer = new System.Threading.Timer(PerformGarbageCollection, null, TimeSpan.FromMinutes(2), TimeSpan.FromMinutes(2));
        //    _performanceTimer = new System.Threading.Timer(LogPerformanceStats, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        //}

        private static void PerformGarbageCollection(object state)
        {
            try
            {
                var currentMemory = GC.GetTotalMemory(false);
                var timeSinceLastGc = DateTime.Now - _lastGcTime;

                // Only force GC if memory usage has increased significantly or enough time has passed
                if (currentMemory > _lastGcMemory * 1.5 || timeSinceLastGc > TimeSpan.FromMinutes(5))
                {
                    ErrorHandler.LogInfo($"Performing GC. Memory before: {currentMemory / 1024 / 1024}MB");

                    GC.Collect(2, GCCollectionMode.Optimized);
                    GC.WaitForPendingFinalizers();
                    GC.Collect(2, GCCollectionMode.Optimized);

                    var memoryAfter = GC.GetTotalMemory(true);
                    ErrorHandler.LogInfo($"GC completed. Memory after: {memoryAfter / 1024 / 1024}MB. " +
                        $"Freed: {(currentMemory - memoryAfter) / 1024 / 1024}MB");

                    _lastGcMemory = memoryAfter;
                    _lastGcTime = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error during garbage collection", ex);
            }
        }

        //private static void LogPerformanceStats(object state)
        //{
        //    try
        //    {
        //        var totalMemory = GC.GetTotalMemory(false);
        //        var gen0Collections = GC.CollectionCount(0);
        //        var gen1Collections = GC.CollectionCount(1);
        //        var gen2Collections = GC.CollectionCount(2);

        //        ErrorHandler.LogInfo($"Performance Stats - Memory: {totalMemory / 1024 / 1024}MB, " +
        //            $"GC Gen0: {gen0Collections}, Gen1: {gen1Collections}, Gen2: {gen2Collections}");

        //        // Log cache sizes
        //        if (InternalMemory.IsInitialized)
        //        {
        //            ErrorHandler.LogInfo($"Cache size: {InternalMemory.GetCacheSize()} entries");
        //        }

        //        if (Core.Entities != null)
        //        {
        //            ErrorHandler.LogInfo($"Entities count: {Core.Entities.Count}");
        //        }

        //        // Log performance counters
        //        PerformanceMonitor.LogPerformanceStats();
        //    }
        //    catch (Exception ex)
        //    {
        //        ErrorHandler.LogError("Error logging performance stats", ex);
        //    }
        //}

        public static void OptimizeForGameLoop()
        {
            try
            {
                // Set thread priority for better performance
                Thread.CurrentThread.Priority = ThreadPriority.AboveNormal;

                // Optimize GC for low latency
                System.Runtime.GCSettings.LatencyMode = System.Runtime.GCLatencyMode.SustainedLowLatency;

                ErrorHandler.LogInfo("Performance optimizations applied");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error applying performance optimizations", ex);
            }
        }

        public static void CleanupResources()
        {
            try
            {
                _gcTimer?.Dispose();
                _performanceTimer?.Dispose();

                // Final cleanup
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                ErrorHandler.LogInfo("Resources cleaned up");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error during resource cleanup", ex);
            }
        }
    }

    // Object pool for frequently used objects
    internal static class ObjectPool<T> where T : class, new()
    {
        private static readonly ConcurrentQueue<T> _objects = new();
        private static readonly Func<T> _objectGenerator = () => new T();
        private const int MaxPoolSize = 100;

        public static T Get()
        {
            if (_objects.TryDequeue(out T item))
                return item;

            return _objectGenerator();
        }

        public static void Return(T item)
        {
            if (item != null && _objects.Count < MaxPoolSize)
            {
                _objects.Enqueue(item);
            }
        }
    }

    // Frame rate limiter
    internal static class FrameRateLimiter
    {
        private static readonly System.Diagnostics.Stopwatch _stopwatch = new();
        private static long _lastFrameTime = 0;

        static FrameRateLimiter()
        {
            _stopwatch.Start();
        }

        public static async Task LimitFrameRate(int targetFps)
        {
            var targetFrameTime = 1000.0 / targetFps; // milliseconds per frame
            var currentTime = _stopwatch.ElapsedMilliseconds;
            var deltaTime = currentTime - _lastFrameTime;

            if (deltaTime < targetFrameTime)
            {
                var sleepTime = (int)(targetFrameTime - deltaTime);
                if (sleepTime > 0)
                {
                    await Task.Delay(sleepTime);
                }
            }

            _lastFrameTime = _stopwatch.ElapsedMilliseconds;
        }

        public static double GetCurrentFps()
        {
            var currentTime = _stopwatch.ElapsedMilliseconds;
            var deltaTime = currentTime - _lastFrameTime;
            return deltaTime > 0 ? 1000.0 / deltaTime : 0;
        }
    }

    // Memory pressure monitor
    internal static class MemoryPressureMonitor
    {
        private static long _lastMemoryCheck = 0;
        private static readonly object _lockObject = new object();

        public static bool IsMemoryPressureHigh()
        {
            lock (_lockObject)
            {
                var currentTime = Environment.TickCount64;
                if (currentTime - _lastMemoryCheck < 5000) // Check every 5 seconds
                    return false;

                _lastMemoryCheck = currentTime;

                var totalMemory = GC.GetTotalMemory(false);
                var availableMemory = GetAvailablePhysicalMemory();

                // Consider memory pressure high if using more than 500MB or less than 100MB available
                var isHighPressure = totalMemory > 500 * 1024 * 1024 || availableMemory < 100 * 1024 * 1024;

                if (isHighPressure)
                {
                    ErrorHandler.LogWarning($"High memory pressure detected. Used: {totalMemory / 1024 / 1024}MB, " +
                        $"Available: {availableMemory / 1024 / 1024}MB");
                }

                return isHighPressure;
            }
        }

        private static long GetAvailablePhysicalMemory()
        {
            try
            {
                using var pc = new System.Diagnostics.PerformanceCounter("Memory", "Available MBytes");
                return (long)(pc.NextValue() * 1024 * 1024);
            }
            catch
            {
                return long.MaxValue; // Assume plenty of memory if we can't check
            }
        }
    }
}
