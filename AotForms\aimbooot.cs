﻿using AotForms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace Client
{
    internal static class Aimbot
    {
        
          internal static void Work()
        {
            Entity lastTarget = null;

            while (true)
            {
                if (!Config.AimBot)
                {
                    Thread.Sleep(5);
                    continue;
                }

                if ((WinAPI.GetAsyncKeyState(Config.AimbotKey) & 0x8) == 0)
                {
                    Thread.Sleep(5);
                    continue;
                }

                Entity target = null;
                float bestDistance = float.MaxValue;

                if (Core.Width == -1 || Core.Height == -1 || !Core.HaveMatrix)
                {
                    Thread.Sleep(5);
                    continue;
                }

                var screenCenter = new Vector2(Core.Width / 2f, Core.Height / 2f);

                foreach (var entity in Core.Entities.Values)
                {
                    if (entity.IsDead) continue;
                    

                    var head2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                    if (head2D.X < 1 || head2D.Y < 1) continue;

                    var distanceToPlayer = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                    if (distanceToPlayer > Config.AimBotMaxDistance) continue;

                    var deltaX = head2D.X - screenCenter.X;
                    var deltaY = head2D.Y - screenCenter.Y;
                    var crosshairDist = (float)Math.Sqrt(deltaX * deltaX + deltaY * deltaY);

                    if (crosshairDist > Config.AimBotFov / 2) continue;

                    if (crosshairDist < bestDistance)
                    {
                        bestDistance = crosshairDist;
                        target = entity;
                    }
                }

                if (target != null)
                {
                 
                    var random = new Random();
                    var headWithRandomness = target.Head;
                    headWithRandomness.X += (float)(random.NextDouble() - 0.5) * 0.1f; 
                    headWithRandomness.Y += (float)(random.NextDouble() - 0.5) * 0.1f;
                    headWithRandomness.Z += (float)(random.NextDouble() - 0.5) * 0.1f;

             
                    if (lastTarget != null && target != lastTarget)
                    {
                        Thread.Sleep(random.Next(20, 50));
                    }

               
                    var aimRotation = MathUtils.GetRotationToLocation(headWithRandomness, Config.Smoothing, Core.LocalMainCamera);

                    InternalMemory.Write(Core.LocalPlayer + Offsets.AimRotation, aimRotation);

                    lastTarget = target;
                }
                else
                {
                    lastTarget = null;
                }

                Thread.Sleep(10); 
            }
        }
    }
}