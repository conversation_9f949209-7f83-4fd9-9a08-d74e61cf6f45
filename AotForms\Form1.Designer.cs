namespace AotForms
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges51 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges52 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges1 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges2 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges3 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges4 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges5 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges6 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges9 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges10 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges7 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges8 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges47 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges48 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges15 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges16 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges11 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges12 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges13 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges14 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges19 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges20 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges17 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges18 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges25 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges26 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges21 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges22 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges23 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges24 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges29 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges30 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges27 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges28 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges35 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges36 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges31 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges32 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges33 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges34 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges39 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges40 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges37 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges38 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges45 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges46 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges41 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges42 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges43 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges44 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges49 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges50 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges55 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges56 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges53 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges54 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges87 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges88 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges61 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges62 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges57 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges58 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges59 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges60 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges67 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges68 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges63 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges64 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges65 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges66 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges73 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges74 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges69 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges70 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges71 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges72 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges77 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges78 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges75 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges76 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges81 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges82 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges79 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges80 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges85 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges86 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges83 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges84 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            guna2Panel11 = new Guna.UI2.WinForms.Guna2Panel();
            guna2ProgressBar1 = new Guna.UI2.WinForms.Guna2ProgressBar();
            guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
            guna2Panel8 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox7 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label8 = new Label();
            guna2Panel7 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel10 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel16 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox9 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label10 = new Label();
            guna2Panel6 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox6 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label7 = new Label();
            guna2Panel2 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel22 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox1 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label2 = new Label();
            guna2Panel5 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox5 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label6 = new Label();
            guna2Panel1 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel15 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox2 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label1 = new Label();
            guna2Panel4 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox4 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label4 = new Label();
            guna2Panel3 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel18 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox3 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label3 = new Label();
            guna2Panel13 = new Guna.UI2.WinForms.Guna2Panel();
            label5 = new Label();
            guna2Panel9 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox8 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label9 = new Label();
            guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(components);
            guna2Panel12 = new Guna.UI2.WinForms.Guna2Panel();
            guna2Panel23 = new Guna.UI2.WinForms.Guna2Panel();
            label15 = new Label();
            guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
            guna2CustomCheckBox12 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Panel21 = new Guna.UI2.WinForms.Guna2Panel();
            label13 = new Label();
            guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
            guna2CustomCheckBox14 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Panel20 = new Guna.UI2.WinForms.Guna2Panel();
            label12 = new Label();
            guna2Button5 = new Guna.UI2.WinForms.Guna2Button();
            guna2CustomCheckBox11 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2Panel14 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox10 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label11 = new Label();
            guna2Panel17 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox13 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label14 = new Label();
            guna2Panel19 = new Guna.UI2.WinForms.Guna2Panel();
            guna2CustomCheckBox15 = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label16 = new Label();
            guna2Panel11.SuspendLayout();
            guna2Panel8.SuspendLayout();
            guna2Panel7.SuspendLayout();
            guna2Panel10.SuspendLayout();
            guna2Panel6.SuspendLayout();
            guna2Panel2.SuspendLayout();
            guna2Panel5.SuspendLayout();
            guna2Panel1.SuspendLayout();
            guna2Panel4.SuspendLayout();
            guna2Panel3.SuspendLayout();
            guna2Panel13.SuspendLayout();
            guna2Panel9.SuspendLayout();
            guna2Panel12.SuspendLayout();
            guna2Panel23.SuspendLayout();
            guna2Panel21.SuspendLayout();
            guna2Panel20.SuspendLayout();
            guna2Panel14.SuspendLayout();
            guna2Panel17.SuspendLayout();
            guna2Panel19.SuspendLayout();
            SuspendLayout();
            //
            // guna2Panel11
            //
            guna2Panel11.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel11.Controls.Add(guna2ProgressBar1);
            guna2Panel11.Controls.Add(guna2Button2);
            guna2Panel11.Controls.Add(guna2Button1);
            guna2Panel11.Controls.Add(guna2Panel8);
            guna2Panel11.Controls.Add(guna2Panel7);
            guna2Panel11.Controls.Add(guna2Panel13);
            guna2Panel11.CustomizableEdges = customizableEdges51;
            guna2Panel11.Location = new Point(0, 0);
            guna2Panel11.Margin = new Padding(4, 3, 4, 3);
            guna2Panel11.Name = "guna2Panel11";
            guna2Panel11.ShadowDecoration.CustomizableEdges = customizableEdges52;
            guna2Panel11.Size = new Size(416, 605);
            guna2Panel11.TabIndex = 439;
            guna2Panel11.Paint += guna2Panel11_Paint;
            //
            // guna2ProgressBar1
            //
            guna2ProgressBar1.CustomizableEdges = customizableEdges1;
            guna2ProgressBar1.Location = new Point(140, 71);
            guna2ProgressBar1.Name = "guna2ProgressBar1";
            guna2ProgressBar1.ShadowDecoration.Color = Color.Red;
            guna2ProgressBar1.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2ProgressBar1.ShadowDecoration.Depth = 60;
            guna2ProgressBar1.ShadowDecoration.Enabled = true;
            guna2ProgressBar1.Size = new Size(54, 3);
            guna2ProgressBar1.TabIndex = 475;
            guna2ProgressBar1.Text = "guna2ProgressBar1";
            guna2ProgressBar1.TextRenderingHint = System.Drawing.Text.TextRenderingHint.SystemDefault;
            //
            // guna2Button2
            //
            guna2Button2.CustomizableEdges = customizableEdges3;
            guna2Button2.DisabledState.BorderColor = Color.DarkGray;
            guna2Button2.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button2.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button2.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button2.FillColor = Color.FromArgb(15, 15, 14);
            guna2Button2.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button2.ForeColor = Color.Gray;
            guna2Button2.Location = new Point(216, 41);
            guna2Button2.Name = "guna2Button2";
            guna2Button2.ShadowDecoration.CustomizableEdges = customizableEdges4;
            guna2Button2.Size = new Size(88, 26);
            guna2Button2.TabIndex = 474;
            guna2Button2.Text = "AIM";
            guna2Button2.Click += guna2Button2_Click;
            //
            // guna2Button1
            //
            guna2Button1.CustomizableEdges = customizableEdges5;
            guna2Button1.DisabledState.BorderColor = Color.DarkGray;
            guna2Button1.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button1.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button1.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button1.FillColor = Color.FromArgb(15, 15, 14);
            guna2Button1.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button1.ForeColor = Color.Gray;
            guna2Button1.Location = new Point(120, 41);
            guna2Button1.Name = "guna2Button1";
            guna2Button1.ShadowDecoration.CustomizableEdges = customizableEdges6;
            guna2Button1.Size = new Size(88, 26);
            guna2Button1.TabIndex = 473;
            guna2Button1.Text = "ESP";
            guna2Button1.Click += guna2Button1_Click;
            //
            // guna2Panel8
            //
            guna2Panel8.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel8.Controls.Add(guna2CustomCheckBox7);
            guna2Panel8.Controls.Add(label8);
            guna2Panel8.CustomizableEdges = customizableEdges9;
            guna2Panel8.Location = new Point(26, 87);
            guna2Panel8.Margin = new Padding(4, 3, 4, 3);
            guna2Panel8.Name = "guna2Panel8";
            guna2Panel8.ShadowDecoration.CustomizableEdges = customizableEdges10;
            guna2Panel8.Size = new Size(364, 39);
            guna2Panel8.TabIndex = 472;
            //
            // guna2CustomCheckBox7
            //
            guna2CustomCheckBox7.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox7.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox7.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox7.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox7.CustomizableEdges = customizableEdges7;
            guna2CustomCheckBox7.Location = new Point(326, 7);
            guna2CustomCheckBox7.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox7.Name = "guna2CustomCheckBox7";
            guna2CustomCheckBox7.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox7.ShadowDecoration.CustomizableEdges = customizableEdges8;
            guna2CustomCheckBox7.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox7.Size = new Size(23, 23);
            guna2CustomCheckBox7.TabIndex = 459;
            guna2CustomCheckBox7.Text = "guna2CustomCheckBox7";
            guna2CustomCheckBox7.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox7.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox7.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox7.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox7.Click += guna2CustomCheckBox7_Click;
            //
            // label8
            //
            label8.AutoSize = true;
            label8.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label8.ForeColor = Color.Gray;
            label8.Location = new Point(6, 10);
            label8.Margin = new Padding(2, 0, 2, 0);
            label8.Name = "label8";
            label8.Size = new Size(91, 18);
            label8.TabIndex = 458;
            label8.Text = "Strat Cheat ";
            //
            // guna2Panel7
            //
            guna2Panel7.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel7.BorderRadius = 8;
            guna2Panel7.BorderThickness = 1;
            guna2Panel7.Controls.Add(guna2Panel10);
            guna2Panel7.Controls.Add(guna2Panel6);
            guna2Panel7.Controls.Add(guna2Panel2);
            guna2Panel7.Controls.Add(guna2Panel5);
            guna2Panel7.Controls.Add(guna2Panel1);
            guna2Panel7.Controls.Add(guna2Panel4);
            guna2Panel7.Controls.Add(guna2Panel3);
            guna2Panel7.CustomizableEdges = customizableEdges47;
            guna2Panel7.Location = new Point(13, 135);
            guna2Panel7.Margin = new Padding(4, 3, 4, 3);
            guna2Panel7.Name = "guna2Panel7";
            guna2Panel7.ShadowDecoration.CustomizableEdges = customizableEdges48;
            guna2Panel7.Size = new Size(390, 419);
            guna2Panel7.TabIndex = 471;
            guna2Panel7.MouseDown += Guna2GradientPanel11_MouseDown;
            guna2Panel7.MouseMove += Guna2GradientPanel11_MouseMove;
            guna2Panel7.MouseUp += Guna2GradientPanel11_MouseUp;
            //
            // guna2Panel10
            //
            guna2Panel10.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel10.Controls.Add(guna2Panel16);
            guna2Panel10.Controls.Add(guna2CustomCheckBox9);
            guna2Panel10.Controls.Add(label10);
            guna2Panel10.CustomizableEdges = customizableEdges15;
            guna2Panel10.Location = new Point(13, 194);
            guna2Panel10.Margin = new Padding(4, 3, 4, 3);
            guna2Panel10.Name = "guna2Panel10";
            guna2Panel10.ShadowDecoration.CustomizableEdges = customizableEdges16;
            guna2Panel10.Size = new Size(364, 39);
            guna2Panel10.TabIndex = 477;
            //
            // guna2Panel16
            //
            guna2Panel16.CustomizableEdges = customizableEdges11;
            guna2Panel16.Location = new Point(113, 16);
            guna2Panel16.Margin = new Padding(4, 3, 4, 3);
            guna2Panel16.Name = "guna2Panel16";
            guna2Panel16.ShadowDecoration.Color = Color.White;
            guna2Panel16.ShadowDecoration.CustomizableEdges = customizableEdges12;
            guna2Panel16.ShadowDecoration.Depth = 50;
            guna2Panel16.ShadowDecoration.Enabled = true;
            guna2Panel16.Size = new Size(204, 4);
            guna2Panel16.TabIndex = 474;
            //
            // guna2CustomCheckBox9
            //
            guna2CustomCheckBox9.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox9.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox9.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox9.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox9.CustomizableEdges = customizableEdges13;
            guna2CustomCheckBox9.Location = new Point(326, 7);
            guna2CustomCheckBox9.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox9.Name = "guna2CustomCheckBox9";
            guna2CustomCheckBox9.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox9.ShadowDecoration.CustomizableEdges = customizableEdges14;
            guna2CustomCheckBox9.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox9.Size = new Size(23, 23);
            guna2CustomCheckBox9.TabIndex = 459;
            guna2CustomCheckBox9.Text = "guna2CustomCheckBox9";
            guna2CustomCheckBox9.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox9.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox9.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox9.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox9.Click += guna2CustomCheckBox9_Click;
            //
            // label10
            //
            label10.AutoSize = true;
            label10.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label10.ForeColor = Color.Gray;
            label10.Location = new Point(6, 10);
            label10.Margin = new Padding(2, 0, 2, 0);
            label10.Name = "label10";
            label10.Size = new Size(101, 18);
            label10.TabIndex = 458;
            label10.Text = "Esp Skeleton";
            //
            // guna2Panel6
            //
            guna2Panel6.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel6.Controls.Add(guna2CustomCheckBox6);
            guna2Panel6.Controls.Add(label7);
            guna2Panel6.CustomizableEdges = customizableEdges19;
            guna2Panel6.Location = new Point(13, 353);
            guna2Panel6.Margin = new Padding(4, 3, 4, 3);
            guna2Panel6.Name = "guna2Panel6";
            guna2Panel6.ShadowDecoration.CustomizableEdges = customizableEdges20;
            guna2Panel6.Size = new Size(364, 39);
            guna2Panel6.TabIndex = 475;
            //
            // guna2CustomCheckBox6
            //
            guna2CustomCheckBox6.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox6.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox6.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox6.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox6.CustomizableEdges = customizableEdges17;
            guna2CustomCheckBox6.Location = new Point(326, 7);
            guna2CustomCheckBox6.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox6.Name = "guna2CustomCheckBox6";
            guna2CustomCheckBox6.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox6.ShadowDecoration.CustomizableEdges = customizableEdges18;
            guna2CustomCheckBox6.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox6.Size = new Size(23, 23);
            guna2CustomCheckBox6.TabIndex = 459;
            guna2CustomCheckBox6.Text = "guna2CustomCheckBox6";
            guna2CustomCheckBox6.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox6.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox6.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox6.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox6.Click += guna2CustomCheckBox6_Click;
            //
            // label7
            //
            label7.AutoSize = true;
            label7.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label7.ForeColor = Color.Gray;
            label7.Location = new Point(6, 10);
            label7.Margin = new Padding(2, 0, 2, 0);
            label7.Name = "label7";
            label7.Size = new Size(107, 18);
            label7.TabIndex = 458;
            label7.Text = "Weapon Icons";
            //
            // guna2Panel2
            //
            guna2Panel2.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel2.Controls.Add(guna2Panel22);
            guna2Panel2.Controls.Add(guna2CustomCheckBox1);
            guna2Panel2.Controls.Add(label2);
            guna2Panel2.CustomizableEdges = customizableEdges25;
            guna2Panel2.Location = new Point(13, 27);
            guna2Panel2.Margin = new Padding(4, 3, 4, 3);
            guna2Panel2.Name = "guna2Panel2";
            guna2Panel2.ShadowDecoration.CustomizableEdges = customizableEdges26;
            guna2Panel2.Size = new Size(364, 39);
            guna2Panel2.TabIndex = 470;
            //
            // guna2Panel22
            //
            guna2Panel22.CustomizableEdges = customizableEdges21;
            guna2Panel22.FillColor = Color.Transparent;
            guna2Panel22.ForeColor = Color.Coral;
            guna2Panel22.Location = new Point(114, 16);
            guna2Panel22.Margin = new Padding(4, 3, 4, 3);
            guna2Panel22.Name = "guna2Panel22";
            guna2Panel22.ShadowDecoration.Color = Color.White;
            guna2Panel22.ShadowDecoration.CustomizableEdges = customizableEdges22;
            guna2Panel22.ShadowDecoration.Depth = 50;
            guna2Panel22.ShadowDecoration.Enabled = true;
            guna2Panel22.Size = new Size(204, 4);
            guna2Panel22.TabIndex = 473;
            //
            // guna2CustomCheckBox1
            //
            guna2CustomCheckBox1.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox1.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox1.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox1.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox1.CustomizableEdges = customizableEdges23;
            guna2CustomCheckBox1.Location = new Point(326, 7);
            guna2CustomCheckBox1.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox1.Name = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox1.ShadowDecoration.CustomizableEdges = customizableEdges24;
            guna2CustomCheckBox1.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox1.Size = new Size(23, 23);
            guna2CustomCheckBox1.TabIndex = 459;
            guna2CustomCheckBox1.Text = "guna2CustomCheckBox1";
            guna2CustomCheckBox1.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox1.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox1.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox1.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox1.Click += guna2CustomCheckBox1_Click;
            //
            // label2
            //
            label2.AutoSize = true;
            label2.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label2.ForeColor = Color.Gray;
            label2.Location = new Point(6, 10);
            label2.Margin = new Padding(2, 0, 2, 0);
            label2.Name = "label2";
            label2.Size = new Size(70, 18);
            label2.TabIndex = 458;
            label2.Text = "Esp Line";
            //
            // guna2Panel5
            //
            guna2Panel5.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel5.Controls.Add(guna2CustomCheckBox5);
            guna2Panel5.Controls.Add(label6);
            guna2Panel5.CustomizableEdges = customizableEdges29;
            guna2Panel5.Location = new Point(13, 298);
            guna2Panel5.Margin = new Padding(4, 3, 4, 3);
            guna2Panel5.Name = "guna2Panel5";
            guna2Panel5.ShadowDecoration.CustomizableEdges = customizableEdges30;
            guna2Panel5.Size = new Size(364, 39);
            guna2Panel5.TabIndex = 474;
            //
            // guna2CustomCheckBox5
            //
            guna2CustomCheckBox5.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox5.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox5.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox5.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox5.CustomizableEdges = customizableEdges27;
            guna2CustomCheckBox5.Location = new Point(326, 7);
            guna2CustomCheckBox5.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox5.Name = "guna2CustomCheckBox5";
            guna2CustomCheckBox5.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox5.ShadowDecoration.CustomizableEdges = customizableEdges28;
            guna2CustomCheckBox5.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox5.Size = new Size(23, 23);
            guna2CustomCheckBox5.TabIndex = 459;
            guna2CustomCheckBox5.Text = "guna2CustomCheckBox5";
            guna2CustomCheckBox5.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox5.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox5.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox5.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox5.Click += guna2CustomCheckBox5_Click;
            //
            // label6
            //
            label6.AutoSize = true;
            label6.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label6.ForeColor = Color.Gray;
            label6.Location = new Point(6, 10);
            label6.Margin = new Padding(2, 0, 2, 0);
            label6.Name = "label6";
            label6.Size = new Size(113, 18);
            label6.TabIndex = 458;
            label6.Text = "Weapon Name";
            //
            // guna2Panel1
            //
            guna2Panel1.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel1.Controls.Add(guna2Panel15);
            guna2Panel1.Controls.Add(guna2CustomCheckBox2);
            guna2Panel1.Controls.Add(label1);
            guna2Panel1.CustomizableEdges = customizableEdges35;
            guna2Panel1.Location = new Point(13, 82);
            guna2Panel1.Margin = new Padding(4, 3, 4, 3);
            guna2Panel1.Name = "guna2Panel1";
            guna2Panel1.ShadowDecoration.CustomizableEdges = customizableEdges36;
            guna2Panel1.Size = new Size(364, 39);
            guna2Panel1.TabIndex = 471;
            //
            // guna2Panel15
            //
            guna2Panel15.CustomizableEdges = customizableEdges31;
            guna2Panel15.Location = new Point(114, 18);
            guna2Panel15.Margin = new Padding(4, 3, 4, 3);
            guna2Panel15.Name = "guna2Panel15";
            guna2Panel15.ShadowDecoration.Color = Color.White;
            guna2Panel15.ShadowDecoration.CustomizableEdges = customizableEdges32;
            guna2Panel15.ShadowDecoration.Depth = 50;
            guna2Panel15.ShadowDecoration.Enabled = true;
            guna2Panel15.Size = new Size(204, 4);
            guna2Panel15.TabIndex = 474;
            //
            // guna2CustomCheckBox2
            //
            guna2CustomCheckBox2.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox2.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox2.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox2.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox2.CustomizableEdges = customizableEdges33;
            guna2CustomCheckBox2.Location = new Point(326, 7);
            guna2CustomCheckBox2.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox2.Name = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox2.ShadowDecoration.CustomizableEdges = customizableEdges34;
            guna2CustomCheckBox2.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox2.Size = new Size(23, 23);
            guna2CustomCheckBox2.TabIndex = 459;
            guna2CustomCheckBox2.Text = "guna2CustomCheckBox2";
            guna2CustomCheckBox2.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox2.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox2.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox2.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox2.Click += guna2CustomCheckBox2_Click;
            //
            // label1
            //
            label1.AutoSize = true;
            label1.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label1.ForeColor = Color.Gray;
            label1.Location = new Point(6, 10);
            label1.Margin = new Padding(2, 0, 2, 0);
            label1.Name = "label1";
            label1.Size = new Size(97, 18);
            label1.TabIndex = 458;
            label1.Text = "Esp Cornerd";
            //
            // guna2Panel4
            //
            guna2Panel4.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel4.Controls.Add(guna2CustomCheckBox4);
            guna2Panel4.Controls.Add(label4);
            guna2Panel4.CustomizableEdges = customizableEdges39;
            guna2Panel4.Location = new Point(13, 246);
            guna2Panel4.Margin = new Padding(4, 3, 4, 3);
            guna2Panel4.Name = "guna2Panel4";
            guna2Panel4.ShadowDecoration.CustomizableEdges = customizableEdges40;
            guna2Panel4.Size = new Size(364, 39);
            guna2Panel4.TabIndex = 473;
            //
            // guna2CustomCheckBox4
            //
            guna2CustomCheckBox4.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox4.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox4.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox4.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox4.CustomizableEdges = customizableEdges37;
            guna2CustomCheckBox4.Location = new Point(326, 7);
            guna2CustomCheckBox4.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox4.Name = "guna2CustomCheckBox4";
            guna2CustomCheckBox4.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox4.ShadowDecoration.CustomizableEdges = customizableEdges38;
            guna2CustomCheckBox4.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox4.Size = new Size(23, 23);
            guna2CustomCheckBox4.TabIndex = 459;
            guna2CustomCheckBox4.Text = "guna2CustomCheckBox4";
            guna2CustomCheckBox4.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox4.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox4.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox4.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox4.Click += guna2CustomCheckBox4_Click;
            //
            // label4
            //
            label4.AutoSize = true;
            label4.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label4.ForeColor = Color.Gray;
            label4.Location = new Point(6, 10);
            label4.Margin = new Padding(2, 0, 2, 0);
            label4.Name = "label4";
            label4.Size = new Size(84, 18);
            label4.TabIndex = 458;
            label4.Text = "Esp Health";
            //
            // guna2Panel3
            //
            guna2Panel3.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel3.Controls.Add(guna2Panel18);
            guna2Panel3.Controls.Add(guna2CustomCheckBox3);
            guna2Panel3.Controls.Add(label3);
            guna2Panel3.CustomizableEdges = customizableEdges45;
            guna2Panel3.Location = new Point(13, 139);
            guna2Panel3.Margin = new Padding(4, 3, 4, 3);
            guna2Panel3.Name = "guna2Panel3";
            guna2Panel3.ShadowDecoration.CustomizableEdges = customizableEdges46;
            guna2Panel3.Size = new Size(364, 39);
            guna2Panel3.TabIndex = 472;
            //
            // guna2Panel18
            //
            guna2Panel18.CustomizableEdges = customizableEdges41;
            guna2Panel18.Location = new Point(114, 16);
            guna2Panel18.Margin = new Padding(4, 3, 4, 3);
            guna2Panel18.Name = "guna2Panel18";
            guna2Panel18.ShadowDecoration.Color = Color.White;
            guna2Panel18.ShadowDecoration.CustomizableEdges = customizableEdges42;
            guna2Panel18.ShadowDecoration.Depth = 50;
            guna2Panel18.ShadowDecoration.Enabled = true;
            guna2Panel18.Size = new Size(204, 4);
            guna2Panel18.TabIndex = 474;
            //
            // guna2CustomCheckBox3
            //
            guna2CustomCheckBox3.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox3.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox3.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox3.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox3.CustomizableEdges = customizableEdges43;
            guna2CustomCheckBox3.Location = new Point(326, 7);
            guna2CustomCheckBox3.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox3.Name = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox3.ShadowDecoration.CustomizableEdges = customizableEdges44;
            guna2CustomCheckBox3.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox3.Size = new Size(23, 23);
            guna2CustomCheckBox3.TabIndex = 459;
            guna2CustomCheckBox3.Text = "guna2CustomCheckBox3";
            guna2CustomCheckBox3.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox3.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox3.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox3.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox3.Click += guna2CustomCheckBox3_Click;
            //
            // label3
            //
            label3.AutoSize = true;
            label3.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label3.ForeColor = Color.Gray;
            label3.Location = new Point(6, 10);
            label3.Margin = new Padding(2, 0, 2, 0);
            label3.Name = "label3";
            label3.Size = new Size(82, 18);
            label3.TabIndex = 458;
            label3.Text = "Esp Name";
            //
            // guna2Panel13
            //
            guna2Panel13.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel13.Controls.Add(label5);
            guna2Panel13.CustomizableEdges = customizableEdges49;
            guna2Panel13.Location = new Point(1, 0);
            guna2Panel13.Margin = new Padding(4, 3, 4, 3);
            guna2Panel13.Name = "guna2Panel13";
            guna2Panel13.ShadowDecoration.CustomizableEdges = customizableEdges50;
            guna2Panel13.Size = new Size(427, 35);
            guna2Panel13.TabIndex = 1;
            //
            // label5
            //
            label5.AutoSize = true;
            label5.BackColor = Color.Transparent;
            label5.Font = new Font("Microsoft Sans Serif", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label5.ForeColor = Color.White;
            label5.Location = new Point(166, 5);
            label5.Margin = new Padding(4, 0, 4, 0);
            label5.Name = "label5";
            label5.Size = new Size(98, 24);
            label5.TabIndex = 424;
            label5.Text = "Esp Line ";
            //
            // guna2Panel9
            //
            guna2Panel9.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel9.Controls.Add(guna2CustomCheckBox8);
            guna2Panel9.Controls.Add(label9);
            guna2Panel9.CustomizableEdges = customizableEdges55;
            guna2Panel9.Location = new Point(14, 184);
            guna2Panel9.Margin = new Padding(4, 3, 4, 3);
            guna2Panel9.Name = "guna2Panel9";
            guna2Panel9.ShadowDecoration.CustomizableEdges = customizableEdges56;
            guna2Panel9.Size = new Size(364, 39);
            guna2Panel9.TabIndex = 476;
            guna2Panel9.Paint += guna2Panel9_Paint;
            //
            // guna2CustomCheckBox8
            //
            guna2CustomCheckBox8.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox8.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox8.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox8.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox8.CustomizableEdges = customizableEdges53;
            guna2CustomCheckBox8.Location = new Point(326, 7);
            guna2CustomCheckBox8.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox8.Name = "guna2CustomCheckBox8";
            guna2CustomCheckBox8.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox8.ShadowDecoration.CustomizableEdges = customizableEdges54;
            guna2CustomCheckBox8.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox8.Size = new Size(23, 23);
            guna2CustomCheckBox8.TabIndex = 459;
            guna2CustomCheckBox8.Text = "guna2CustomCheckBox8";
            guna2CustomCheckBox8.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox8.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox8.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox8.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox8.Click += guna2CustomCheckBox8_Click;
            //
            // label9
            //
            label9.AutoSize = true;
            label9.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label9.ForeColor = Color.Gray;
            label9.Location = new Point(6, 10);
            label9.Margin = new Padding(2, 0, 2, 0);
            label9.Name = "label9";
            label9.Size = new Size(58, 18);
            label9.TabIndex = 458;
            label9.Text = "Aimbot";
            //
            // guna2BorderlessForm1
            //
            guna2BorderlessForm1.BorderRadius = 8;
            guna2BorderlessForm1.ContainerControl = this;
            guna2BorderlessForm1.DockIndicatorColor = Color.Gold;
            guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6D;
            guna2BorderlessForm1.ShadowColor = Color.Gold;
            guna2BorderlessForm1.TransparentWhileDrag = true;
            //
            // guna2Panel12
            //
            guna2Panel12.BackColor = Color.FromArgb(10, 10, 10);
            guna2Panel12.BorderRadius = 8;
            guna2Panel12.BorderThickness = 1;
            guna2Panel12.Controls.Add(guna2Panel23);
            guna2Panel12.Controls.Add(guna2Panel21);
            guna2Panel12.Controls.Add(guna2Panel20);
            guna2Panel12.Controls.Add(guna2Panel14);
            guna2Panel12.Controls.Add(guna2Panel9);
            guna2Panel12.Controls.Add(guna2Panel17);
            guna2Panel12.Controls.Add(guna2Panel19);
            guna2Panel12.CustomizableEdges = customizableEdges87;
            guna2Panel12.Location = new Point(472, 132);
            guna2Panel12.Margin = new Padding(4, 3, 4, 3);
            guna2Panel12.Name = "guna2Panel12";
            guna2Panel12.ShadowDecoration.CustomizableEdges = customizableEdges88;
            guna2Panel12.Size = new Size(390, 459);
            guna2Panel12.TabIndex = 472;
            //
            // guna2Panel23
            //
            guna2Panel23.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel23.Controls.Add(label15);
            guna2Panel23.Controls.Add(guna2Button4);
            guna2Panel23.Controls.Add(guna2CustomCheckBox12);
            guna2Panel23.CustomizableEdges = customizableEdges61;
            guna2Panel23.Location = new Point(14, 319);
            guna2Panel23.Margin = new Padding(4, 3, 4, 3);
            guna2Panel23.Name = "guna2Panel23";
            guna2Panel23.ShadowDecoration.CustomizableEdges = customizableEdges62;
            guna2Panel23.Size = new Size(364, 39);
            guna2Panel23.TabIndex = 483;
            //
            // label15
            //
            label15.AutoSize = true;
            label15.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label15.ForeColor = Color.Gray;
            label15.Location = new Point(6, 10);
            label15.Margin = new Padding(2, 0, 2, 0);
            label15.Name = "label15";
            label15.Size = new Size(91, 18);
            label15.TabIndex = 458;
            label15.Text = "down player";
            //
            // guna2Button4
            //
            guna2Button4.CustomizableEdges = customizableEdges57;
            guna2Button4.DisabledState.BorderColor = Color.DarkGray;
            guna2Button4.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button4.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button4.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button4.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button4.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button4.ForeColor = Color.DimGray;
            guna2Button4.Image = (Image)resources.GetObject("guna2Button4.Image");
            guna2Button4.Location = new Point(225, 9);
            guna2Button4.Margin = new Padding(4, 3, 4, 3);
            guna2Button4.Name = "guna2Button4";
            guna2Button4.ShadowDecoration.Color = Color.DimGray;
            guna2Button4.ShadowDecoration.CustomizableEdges = customizableEdges58;
            guna2Button4.ShadowDecoration.Enabled = true;
            guna2Button4.Size = new Size(93, 21);
            guna2Button4.TabIndex = 478;
            guna2Button4.Text = "None";
            guna2Button4.Click += guna2Button4_Click;
            //
            // guna2CustomCheckBox12
            //
            guna2CustomCheckBox12.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox12.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox12.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox12.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox12.CustomizableEdges = customizableEdges59;
            guna2CustomCheckBox12.Location = new Point(327, 7);
            guna2CustomCheckBox12.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox12.Name = "guna2CustomCheckBox12";
            guna2CustomCheckBox12.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox12.ShadowDecoration.CustomizableEdges = customizableEdges60;
            guna2CustomCheckBox12.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox12.Size = new Size(23, 23);
            guna2CustomCheckBox12.TabIndex = 477;
            guna2CustomCheckBox12.Text = "guna2CustomCheckBox12";
            guna2CustomCheckBox12.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox12.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox12.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox12.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox12.Click += guna2CustomCheckBox12_CheckedChanged;
            //
            // guna2Panel21
            //
            guna2Panel21.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel21.Controls.Add(label13);
            guna2Panel21.Controls.Add(guna2Button3);
            guna2Panel21.Controls.Add(guna2CustomCheckBox14);
            guna2Panel21.CustomizableEdges = customizableEdges67;
            guna2Panel21.Location = new Point(13, 274);
            guna2Panel21.Margin = new Padding(4, 3, 4, 3);
            guna2Panel21.Name = "guna2Panel21";
            guna2Panel21.ShadowDecoration.CustomizableEdges = customizableEdges68;
            guna2Panel21.Size = new Size(364, 39);
            guna2Panel21.TabIndex = 482;
            guna2Panel21.Paint += guna2Panel21_Paint;
            //
            // label13
            //
            label13.AutoSize = true;
            label13.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label13.ForeColor = Color.Gray;
            label13.Location = new Point(6, 10);
            label13.Margin = new Padding(2, 0, 2, 0);
            label13.Name = "label13";
            label13.Size = new Size(140, 18);
            label13.TabIndex = 458;
            label13.Text = "Auto Teleport to Bone";
            //
            // guna2Button3
            //
            guna2Button3.CustomizableEdges = customizableEdges63;
            guna2Button3.DisabledState.BorderColor = Color.DarkGray;
            guna2Button3.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button3.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button3.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button3.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button3.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button3.ForeColor = Color.DimGray;
            guna2Button3.Image = (Image)resources.GetObject("guna2Button3.Image");
            guna2Button3.Location = new Point(225, 9);
            guna2Button3.Margin = new Padding(4, 3, 4, 3);
            guna2Button3.Name = "guna2Button3";
            guna2Button3.ShadowDecoration.Color = Color.DimGray;
            guna2Button3.ShadowDecoration.CustomizableEdges = customizableEdges64;
            guna2Button3.ShadowDecoration.Enabled = true;
            guna2Button3.Size = new Size(93, 21);
            guna2Button3.TabIndex = 478;
            guna2Button3.Text = "Settings";
            guna2Button3.Click += guna2Button6_Click;
            //
            // guna2CustomCheckBox14
            //
            guna2CustomCheckBox14.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox14.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox14.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox14.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox14.CustomizableEdges = customizableEdges65;
            guna2CustomCheckBox14.Location = new Point(327, 9);
            guna2CustomCheckBox14.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox14.Name = "guna2CustomCheckBox14";
            guna2CustomCheckBox14.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox14.ShadowDecoration.CustomizableEdges = customizableEdges66;
            guna2CustomCheckBox14.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox14.Size = new Size(23, 23);
            guna2CustomCheckBox14.TabIndex = 477;
            guna2CustomCheckBox14.Text = "guna2CustomCheckBox14";
            guna2CustomCheckBox14.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox14.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox14.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox14.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox14.Click += guna2CustomCheckBox14_CheckedChanged_AutoTeleport;
            //
            // guna2Panel20
            //
            guna2Panel20.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel20.Controls.Add(label12);
            guna2Panel20.Controls.Add(guna2Button5);
            guna2Panel20.Controls.Add(guna2CustomCheckBox11);
            guna2Panel20.CustomizableEdges = customizableEdges73;
            guna2Panel20.Location = new Point(13, 229);
            guna2Panel20.Margin = new Padding(4, 3, 4, 3);
            guna2Panel20.Name = "guna2Panel20";
            guna2Panel20.ShadowDecoration.CustomizableEdges = customizableEdges74;
            guna2Panel20.Size = new Size(364, 39);
            guna2Panel20.TabIndex = 481;
            //
            // label12
            //
            label12.AutoSize = true;
            label12.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label12.ForeColor = Color.Gray;
            label12.Location = new Point(6, 10);
            label12.Margin = new Padding(2, 0, 2, 0);
            label12.Name = "label12";
            label12.Size = new Size(66, 18);
            label12.TabIndex = 458;
            label12.Text = "down up";
            //
            // guna2Button5
            //
            guna2Button5.CustomizableEdges = customizableEdges69;
            guna2Button5.DisabledState.BorderColor = Color.DarkGray;
            guna2Button5.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button5.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button5.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button5.FillColor = Color.FromArgb(10, 10, 15);
            guna2Button5.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button5.ForeColor = Color.DimGray;
            guna2Button5.Image = (Image)resources.GetObject("guna2Button5.Image");
            guna2Button5.Location = new Point(225, 9);
            guna2Button5.Margin = new Padding(4, 3, 4, 3);
            guna2Button5.Name = "guna2Button5";
            guna2Button5.ShadowDecoration.Color = Color.DimGray;
            guna2Button5.ShadowDecoration.CustomizableEdges = customizableEdges70;
            guna2Button5.ShadowDecoration.Enabled = true;
            guna2Button5.Size = new Size(93, 21);
            guna2Button5.TabIndex = 478;
            guna2Button5.Text = "None";
            guna2Button5.Click += guna2Button5_Click;
            //
            // guna2CustomCheckBox11
            //
            guna2CustomCheckBox11.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox11.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox11.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox11.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox11.CustomizableEdges = customizableEdges71;
            guna2CustomCheckBox11.Location = new Point(327, 7);
            guna2CustomCheckBox11.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox11.Name = "guna2CustomCheckBox11";
            guna2CustomCheckBox11.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox11.ShadowDecoration.CustomizableEdges = customizableEdges72;
            guna2CustomCheckBox11.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox11.Size = new Size(23, 23);
            guna2CustomCheckBox11.TabIndex = 477;
            guna2CustomCheckBox11.Text = "guna2CustomCheckBox11";
            guna2CustomCheckBox11.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox11.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox11.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox11.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox11.Click += guna2CustomCheckBox11_CheckedChanged;
            //
            // guna2Panel14
            //
            guna2Panel14.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel14.Controls.Add(guna2CustomCheckBox10);
            guna2Panel14.Controls.Add(label11);
            guna2Panel14.CustomizableEdges = customizableEdges77;
            guna2Panel14.Location = new Point(14, 134);
            guna2Panel14.Margin = new Padding(4, 3, 4, 3);
            guna2Panel14.Name = "guna2Panel14";
            guna2Panel14.ShadowDecoration.CustomizableEdges = customizableEdges78;
            guna2Panel14.Size = new Size(364, 39);
            guna2Panel14.TabIndex = 472;
            guna2Panel14.Paint += guna2Panel14_Paint;
            //
            // guna2CustomCheckBox10
            //
            guna2CustomCheckBox10.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox10.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox10.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox10.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox10.CustomizableEdges = customizableEdges75;
            guna2CustomCheckBox10.Location = new Point(326, 7);
            guna2CustomCheckBox10.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox10.Name = "guna2CustomCheckBox10";
            guna2CustomCheckBox10.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox10.ShadowDecoration.CustomizableEdges = customizableEdges76;
            guna2CustomCheckBox10.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox10.Size = new Size(23, 23);
            guna2CustomCheckBox10.TabIndex = 459;
            guna2CustomCheckBox10.Text = "guna2CustomCheckBox10";
            guna2CustomCheckBox10.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox10.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox10.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox10.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox10.Click += guna2CustomCheckBox10_Click;
            //
            // label11
            //
            label11.AutoSize = true;
            label11.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label11.ForeColor = Color.Gray;
            label11.Location = new Point(6, 10);
            label11.Margin = new Padding(2, 0, 2, 0);
            label11.Name = "label11";
            label11.Size = new Size(93, 18);
            label11.TabIndex = 458;
            label11.Text = "Awm Switch";
            //
            // guna2Panel17
            //
            guna2Panel17.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel17.Controls.Add(guna2CustomCheckBox13);
            guna2Panel17.Controls.Add(label14);
            guna2Panel17.CustomizableEdges = customizableEdges81;
            guna2Panel17.Location = new Point(13, 27);
            guna2Panel17.Margin = new Padding(4, 3, 4, 3);
            guna2Panel17.Name = "guna2Panel17";
            guna2Panel17.ShadowDecoration.CustomizableEdges = customizableEdges82;
            guna2Panel17.Size = new Size(364, 39);
            guna2Panel17.TabIndex = 470;
            guna2Panel17.Paint += guna2Panel17_Paint;
            //
            // guna2CustomCheckBox13
            //
            guna2CustomCheckBox13.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox13.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox13.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox13.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox13.CustomizableEdges = customizableEdges79;
            guna2CustomCheckBox13.Location = new Point(326, 7);
            guna2CustomCheckBox13.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox13.Name = "guna2CustomCheckBox13";
            guna2CustomCheckBox13.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox13.ShadowDecoration.CustomizableEdges = customizableEdges80;
            guna2CustomCheckBox13.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox13.Size = new Size(23, 23);
            guna2CustomCheckBox13.TabIndex = 459;
            guna2CustomCheckBox13.Text = "guna2CustomCheckBox13";
            guna2CustomCheckBox13.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox13.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox13.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox13.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox13.Click += guna2CustomCheckBox13_Click;
            //
            // label14
            //
            label14.AutoSize = true;
            label14.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label14.ForeColor = Color.Gray;
            label14.Location = new Point(6, 10);
            label14.Margin = new Padding(2, 0, 2, 0);
            label14.Name = "label14";
            label14.Size = new Size(104, 18);
            label14.TabIndex = 458;
            label14.Text = "Start Funciont";
            //
            // guna2Panel19
            //
            guna2Panel19.BackColor = Color.FromArgb(15, 15, 14);
            guna2Panel19.Controls.Add(guna2CustomCheckBox15);
            guna2Panel19.Controls.Add(label16);
            guna2Panel19.CustomizableEdges = customizableEdges85;
            guna2Panel19.Location = new Point(13, 82);
            guna2Panel19.Margin = new Padding(4, 3, 4, 3);
            guna2Panel19.Name = "guna2Panel19";
            guna2Panel19.ShadowDecoration.CustomizableEdges = customizableEdges86;
            guna2Panel19.Size = new Size(364, 39);
            guna2Panel19.TabIndex = 471;
            //
            // guna2CustomCheckBox15
            //
            guna2CustomCheckBox15.CheckedState.BorderColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox15.CheckedState.BorderRadius = 2;
            guna2CustomCheckBox15.CheckedState.BorderThickness = 0;
            guna2CustomCheckBox15.CheckedState.FillColor = Color.FromArgb(70, 70, 70);
            guna2CustomCheckBox15.CustomizableEdges = customizableEdges83;
            guna2CustomCheckBox15.Location = new Point(326, 7);
            guna2CustomCheckBox15.Margin = new Padding(4, 3, 4, 3);
            guna2CustomCheckBox15.Name = "guna2CustomCheckBox15";
            guna2CustomCheckBox15.ShadowDecoration.Color = Color.FromArgb(15, 15, 14);
            guna2CustomCheckBox15.ShadowDecoration.CustomizableEdges = customizableEdges84;
            guna2CustomCheckBox15.ShadowDecoration.Enabled = true;
            guna2CustomCheckBox15.Size = new Size(23, 23);
            guna2CustomCheckBox15.TabIndex = 459;
            guna2CustomCheckBox15.Text = "guna2CustomCheckBox15";
            guna2CustomCheckBox15.UncheckedState.BorderColor = Color.Gray;
            guna2CustomCheckBox15.UncheckedState.BorderRadius = 2;
            guna2CustomCheckBox15.UncheckedState.BorderThickness = 0;
            guna2CustomCheckBox15.UncheckedState.FillColor = Color.FromArgb(40, 40, 40);
            guna2CustomCheckBox15.Click += guna2CustomCheckBox15_Click;
            //
            // label16
            //
            label16.AutoSize = true;
            label16.Font = new Font("Arial", 12F, FontStyle.Regular, GraphicsUnit.Point);
            label16.ForeColor = Color.Gray;
            label16.Location = new Point(6, 10);
            label16.Margin = new Padding(2, 0, 2, 0);
            label16.Name = "label16";
            label16.Size = new Size(84, 18);
            label16.TabIndex = 458;
            label16.Text = "Awm Scop";
            //
            // Form1
            //
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.Black;
            ClientSize = new Size(1010, 722);
            Controls.Add(guna2Panel12);
            Controls.Add(guna2Panel11);
            FormBorderStyle = FormBorderStyle.None;
            Name = "Form1";
            Text = "Form1";
            Load += Form1_Load;
            guna2Panel11.ResumeLayout(false);
            guna2Panel8.ResumeLayout(false);
            guna2Panel8.PerformLayout();
            guna2Panel7.ResumeLayout(false);
            guna2Panel10.ResumeLayout(false);
            guna2Panel10.PerformLayout();
            guna2Panel6.ResumeLayout(false);
            guna2Panel6.PerformLayout();
            guna2Panel2.ResumeLayout(false);
            guna2Panel2.PerformLayout();
            guna2Panel5.ResumeLayout(false);
            guna2Panel5.PerformLayout();
            guna2Panel1.ResumeLayout(false);
            guna2Panel1.PerformLayout();
            guna2Panel4.ResumeLayout(false);
            guna2Panel4.PerformLayout();
            guna2Panel3.ResumeLayout(false);
            guna2Panel3.PerformLayout();
            guna2Panel13.ResumeLayout(false);
            guna2Panel13.PerformLayout();
            guna2Panel9.ResumeLayout(false);
            guna2Panel9.PerformLayout();
            guna2Panel12.ResumeLayout(false);
            guna2Panel23.ResumeLayout(false);
            guna2Panel23.PerformLayout();
            guna2Panel21.ResumeLayout(false);
            guna2Panel21.PerformLayout();
            guna2Panel20.ResumeLayout(false);
            guna2Panel20.PerformLayout();
            guna2Panel14.ResumeLayout(false);
            guna2Panel14.PerformLayout();
            guna2Panel17.ResumeLayout(false);
            guna2Panel17.PerformLayout();
            guna2Panel19.ResumeLayout(false);
            guna2Panel19.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private Guna.UI2.WinForms.Guna2Panel guna2Panel11;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel13;
        private Label label5;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel6;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox6;
        private Label label7;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel5;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox5;
        private Label label6;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel4;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox4;
        private Label label4;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel3;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox3;
        private Label label3;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel1;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox2;
        private Label label1;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel2;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox1;
        private Label label2;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel8;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox7;
        private Label label8;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel7;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel9;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox8;
        private Label label9;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel10;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox9;
        private Label label10;
        private Guna.UI2.WinForms.Guna2BorderlessForm guna2BorderlessForm1;
        private Guna.UI2.WinForms.Guna2Button guna2Button1;
        private Guna.UI2.WinForms.Guna2ProgressBar guna2ProgressBar1;
        private Guna.UI2.WinForms.Guna2Button guna2Button2;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel12;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel17;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox13;
        private Label label14;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel19;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox15;
        private Label label16;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel14;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox10;
        private Label label11;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel16;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel22;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel15;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel18;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox11;
        private Guna.UI2.WinForms.Guna2Button guna2Button5;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel20;
        private Label label12;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel21;
        private Label label13;
        private Guna.UI2.WinForms.Guna2Button guna2Button3;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox14;
        private Guna.UI2.WinForms.Guna2Panel guna2Panel23;
        private Label label15;
        private Guna.UI2.WinForms.Guna2Button guna2Button4;
        private Guna.UI2.WinForms.Guna2CustomCheckBox guna2CustomCheckBox12;
    }
}