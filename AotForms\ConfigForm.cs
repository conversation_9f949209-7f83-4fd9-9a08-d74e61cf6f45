﻿using AotForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Client
{
    public partial class ConfigForm : Form
    {
        public ConfigForm()
        {
            InitializeComponent();
            guna2ComboBox1.DataSource = Enum.GetValues(typeof(Keys)).Cast<Keys>().ToList();
            guna2ComboBox1.SelectedItem = Keys.LButton;
        }

        private void ConfigForm_Load(object sender, EventArgs e)
        {
            this.TopMost = false;
        }

        private void guna2ComboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void guna2Button2_Click(object sender, EventArgs e)
        {
            Config.Notif();
        }

        private void guna2Button1_Click(object sender, EventArgs e)
        {
            Config.Notif();
        }

        private void guna2Button3_Click(object sender, EventArgs e)
        {
            // فتح نافذة إعدادات التحريك التلقائي
            AutoTeleportManager.ShowConfigWindow();
            Config.Notif();
        }
    }
}
