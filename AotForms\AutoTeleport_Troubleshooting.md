# استكشاف أخطاء AutoTeleportToBone وإصلاحها

## 🔍 المشاكل المحتملة وحلولها

### 1. ❌ النظام لا يعمل عند تفعيل الـ Checkbox

#### الأسباب المحتملة:
- **عدم تشغيل النظام**: النظام يحتاج لتشغيل صريح
- **عدم وجود أعداء**: النظام يبحث عن أعداء صالحين
- **مشاكل في قراءة الذاكرة**: فشل في قراءة مواقع اللاعبين

#### ✅ الحلول المطبقة:
```csharp
// تشغيل تلقائي عند تفعيل الـ checkbox
private void guna2CustomCheckBox14_CheckedChanged_AutoTeleport(object sender, EventArgs e)
{
    Config.AutoTeleportToBone = guna2CustomCheckBox14.Checked;
    
    if (Config.AutoTeleportToBone)
    {
        AutoTeleportToBone.Start(); // تشغيل النظام
    }
    else
    {
        AutoTeleportToBone.Stop(); // إيقاف النظام
    }
    
    Config.Notif();
}
```

### 2. ❌ المفاتيح F5/F6 لا تعمل

#### ✅ الحل المطبق:
```csharp
// إضافة hook جديد للتحكم في F5/F6
private IntPtr HookCallback6(int nCode, IntPtr wParam, IntPtr lParam)
{
    if (nCode >= 0 && wParam == (IntPtr)WM_KEYDOWN6)
    {
        Keys key = (Keys)Marshal.ReadInt32(lParam);

        // F5 - تشغيل/إيقاف
        if (key == Keys.F5)
        {
            Config.AutoTeleportToBone = !Config.AutoTeleportToBone;
            guna2CustomCheckBox14.Checked = Config.AutoTeleportToBone;
            
            if (Config.AutoTeleportToBone)
                AutoTeleportToBone.Start();
            else
                AutoTeleportToBone.Stop();
        }
        // F6 - فتح الإعدادات
        else if (key == Keys.F6)
        {
            AutoTeleportManager.ShowConfigWindow();
        }
    }
    return CallNextHookEx(hookID6, nCode, wParam, lParam);
}
```

### 3. ❌ نافذة الإعدادات لا تفتح

#### ✅ الحل المطبق:
```csharp
// إنشاء AutoTeleportManager.cs مع نافذة إعدادات كاملة
public static void ShowConfigWindow()
{
    if (isConfigWindowOpen && configWindow != null && !configWindow.IsDisposed)
    {
        configWindow.BringToFront();
        return;
    }
    CreateConfigWindow();
}
```

### 4. ❌ النظام لا يستجيب للإعدادات

#### ✅ الحل المطبق:
```csharp
// ربط خصائص AutoTeleportToBone مع Config
public static float TeleportSpeed 
{ 
    get => Config.AutoTeleportSpeed; 
    set => Config.AutoTeleportSpeed = value; 
}
```

## 🔧 خطوات التشخيص

### 1. **فحص تشغيل النظام**
```csharp
// تحقق من أن النظام يعمل
Console.WriteLine($"AutoTeleport Status: {Config.AutoTeleportToBone}");
Console.WriteLine($"Task Running: {AutoTeleportToBone.IsRunning}");
```

### 2. **فحص وجود الأعداء**
```csharp
// تحقق من وجود أعداء في اللعبة
Console.WriteLine($"Entities Count: {Core.Entities?.Count ?? 0}");
foreach (var entity in Core.Entities.Values)
{
    Console.WriteLine($"Entity: {entity.Address:X}, Dead: {entity.IsDead}, Known: {entity.IsKnown}");
}
```

### 3. **فحص قراءة الذاكرة**
```csharp
// تحقق من قراءة موقع اللاعب المحلي
if (GetLocalPlayerPosition(out Vector3 pos))
{
    Console.WriteLine($"Local Player Position: {pos}");
}
else
{
    Console.WriteLine("Failed to read local player position");
}
```

### 4. **فحص العظام المستهدفة**
```csharp
// تحقق من قراءة عظام الأعداء
foreach (var enemy in Core.Entities.Values)
{
    if (enemy.IsDead || !enemy.IsKnown) continue;
    
    foreach (var bone in TargetBones)
    {
        if (InternalMemory.Read<uint>(enemy.Address + (uint)bone, out var boneAddr))
        {
            Console.WriteLine($"Bone {bone}: {boneAddr:X}");
        }
    }
}
```

## 🎯 نصائح للاستخدام

### 1. **التأكد من تشغيل اللعبة**
- تأكد من أن اللعبة (HD-Player) تعمل
- تأكد من أن ESP يعمل بشكل صحيح
- تأكد من وجود أعداء في الخريطة

### 2. **ضبط الإعدادات**
- **للمبتدئين**: سرعة 0.05، مسافة 3.0m، تأخير 100ms
- **للمتقدمين**: سرعة 0.2، مسافة 1.5m، تأخير 25ms

### 3. **استخدام المفاتيح**
- **F5**: تشغيل/إيقاف سريع
- **F6**: فتح نافذة الإعدادات
- **Checkbox**: تحكم عبر الواجهة

### 4. **مراقبة الأداء**
- راقب استهلاك CPU
- تأكد من عدم وجود lag في اللعبة
- اضبط تأخير التحديث حسب الحاجة

## 🚨 تحذيرات مهمة

### 1. **الأمان**
- لا تستخدم سرعات عالية جداً (أكثر من 0.5)
- تجنب المسافات القريبة جداً (أقل من 1.0m)
- استخدم تأخير مناسب لتجنب الكشف

### 2. **الاستقرار**
- أعد تشغيل النظام إذا توقف
- تحقق من حالة الذاكرة بانتظام
- أغلق النظام عند عدم الحاجة

### 3. **التوافق**
- تأكد من تحديث offsets إذا تم تحديث اللعبة
- تحقق من عمل ESP قبل استخدام AutoTeleport
- استخدم النظام مع الأنظمة الأخرى بحذر

## 📊 حالات الاستخدام

### 1. **الوضع الدفاعي**
```
السرعة: 0.03
المسافة: 5.0m
التأخير: 100ms
الاستخدام: للبقاء آمناً والتحرك ببطء
```

### 2. **الوضع المتوازن**
```
السرعة: 0.1
المسافة: 2.0m
التأخير: 50ms
الاستخدام: للاستخدام العادي
```

### 3. **الوضع العدواني**
```
السرعة: 0.3
المسافة: 1.0m
التأخير: 25ms
الاستخدام: للهجوم السريع (خطر!)
```

## ✅ قائمة التحقق

- [ ] تم تشغيل اللعبة (HD-Player)
- [ ] ESP يعمل ويظهر الأعداء
- [ ] تم تفعيل AutoTeleportToBone من الـ checkbox
- [ ] تم ضبط الإعدادات المناسبة
- [ ] تم اختبار المفاتيح F5/F6
- [ ] النظام يستجيب للتغييرات
- [ ] لا توجد أخطاء في Console
- [ ] الأداء مستقر

إذا استمرت المشاكل، تحقق من Console للأخطاء وتأكد من أن جميع المتطلبات متوفرة.
