using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Threading;
using System.Threading.Tasks;

namespace AotForms
{
    internal static class AutoTeleportToBone
    {
        private static Task teleportTask;
        private static CancellationTokenSource cts = new CancellationTokenSource();

        // إعدادات التحكم في السرعة والسلوك - تستخدم إعدادات Config
        public static float TeleportSpeed
        {
            get => Config.AutoTeleportSpeed;
            set => Config.AutoTeleportSpeed = value;
        }
        public static float MinDistanceToTarget
        {
            get => Config.AutoTeleportMinDistance;
            set => Config.AutoTeleportMinDistance = value;
        }
        public static int UpdateDelayMs
        {
            get => Config.AutoTeleportUpdateDelay;
            set => Config.AutoTeleportUpdateDelay = value;
        }
        public static bool IsEnabled
        {
            get => Config.AutoTeleportToBone;
            set => Config.AutoTeleportToBone = value;
        }

        // العظام المستهدفة للبحث عن أقرب نقطة
        private static readonly Bones[] TargetBones = new Bones[]
        {
            Bones.Head,
            Bones.Neck,
            Bones.Pelvis,
            Bones.ShoulderL,
            Bones.ShoulderR,
            Bones.ElbowL,
            Bones.ElbowR,
            Bones.HandL,
            Bones.HandR,
            Bones.FootL,
            Bones.FootR,
            Bones.KneeL,
            Bones.KneeR,
            Bones.Hip,
            Bones.Spine
        };

        /// <summary>
        /// بدء تشغيل نظام التحريك التلقائي
        /// </summary>
        public static void Start()
        {
            if (teleportTask != null && !teleportTask.IsCompleted)
                return;

            cts = new CancellationTokenSource();
            teleportTask = Task.Run(AutoTeleportLoop, cts.Token);
        }

        /// <summary>
        /// إيقاف نظام التحريك التلقائي
        /// </summary>
        public static void Stop()
        {
            cts?.Cancel();
            IsEnabled = false;
        }

        /// <summary>
        /// الحلقة الرئيسية للتحريك التلقائي
        /// </summary>
        private static async Task AutoTeleportLoop()
        {
            while (!cts.Token.IsCancellationRequested)
            {
                try
                {
                    if (!IsEnabled)
                    {
                        await Task.Delay(100, cts.Token);
                        continue;
                    }

                    // البحث عن أقرب عدو صالح
                    var closestEnemy = FindClosestValidEnemy();
                    if (closestEnemy == null)
                    {
                        await Task.Delay(UpdateDelayMs, cts.Token);
                        continue;
                    }

                    // البحث عن أقرب عظمة للعدو
                    var closestBonePosition = FindClosestBonePosition(closestEnemy);
                    if (closestBonePosition == Vector3.Zero)
                    {
                        await Task.Delay(UpdateDelayMs, cts.Token);
                        continue;
                    }

                    // التحريك التدريجي نحو العظمة المستهدفة
                    await TeleportToBone(closestBonePosition);
                }
                catch (Exception ex)
                {
                    // تجنب الانهيار في حالة حدوث خطأ
                    Console.WriteLine($"AutoTeleportToBone Error: {ex.Message}");
                    await Task.Delay(1000, cts.Token);
                }

                await Task.Delay(UpdateDelayMs, cts.Token);
            }
        }

        /// <summary>
        /// البحث عن أقرب عدو صالح (حي ومعروف)
        /// </summary>
        private static Entity FindClosestValidEnemy()
        {
            if (Core.Entities == null || Core.Entities.IsEmpty)
                return null;

            Entity closestEnemy = null;
            float minDistance = float.MaxValue;

            foreach (var entity in Core.Entities.Values)
            {
                // تجاهل الأعداء الموتى أو غير المعروفين
                if (entity.IsDead || entity.IsKnocked || !entity.IsKnown)
                    continue;

                // التأكد من صحة العنوان
                if (entity.Address == 0 || entity.BaseAddress == IntPtr.Zero)
                    continue;

                // حساب المسافة
                float distance = entity.Distance;
                if (distance < minDistance && distance > 0)
                {
                    minDistance = distance;
                    closestEnemy = entity;
                }
            }

            return closestEnemy;
        }

        /// <summary>
        /// البحث عن أقرب عظمة للعدو المحدد
        /// </summary>
        private static Vector3 FindClosestBonePosition(Entity enemy)
        {
            if (enemy == null || enemy.Address == 0)
                return Vector3.Zero;

            Vector3 closestBonePosition = Vector3.Zero;
            float minDistance = float.MaxValue;

            // الحصول على موقع اللاعب المحلي
            if (!GetLocalPlayerPosition(out Vector3 localPlayerPos))
                return Vector3.Zero;

            foreach (var bone in TargetBones)
            {
                try
                {
                    // قراءة عنوان العظمة
                    if (!InternalMemory.Read<uint>(enemy.Address + (uint)bone, out var boneAddress) || boneAddress == 0)
                        continue;

                    // الحصول على موقع العظمة
                    if (!Transform.GetNodePosition(boneAddress, out Vector3 bonePosition))
                        continue;

                    // حساب المسافة من اللاعب المحلي إلى العظمة
                    float distance = Vector3.Distance(localPlayerPos, bonePosition);

                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        closestBonePosition = bonePosition;
                    }
                }
                catch
                {
                    // تجاهل الأخطاء وتابع مع العظمة التالية
                    continue;
                }
            }

            return closestBonePosition;
        }

        /// <summary>
        /// التحريك التدريجي نحو العظمة المستهدفة
        /// </summary>
        private static async Task TeleportToBone(Vector3 targetPosition)
        {
            if (!GetLocalPlayerPosition(out Vector3 currentPosition))
                return;

            // حساب المسافة إلى الهدف
            float distanceToTarget = Vector3.Distance(currentPosition, targetPosition);

            // إذا كنا قريبين بما فيه الكفاية، توقف
            if (distanceToTarget <= MinDistanceToTarget)
                return;

            // حساب الموقع الجديد باستخدام التحريك التدريجي
            Vector3 newPosition = Vector3.Lerp(currentPosition, targetPosition, TeleportSpeed);

            // تطبيق الموقع الجديد
            SetLocalPlayerPosition(newPosition);
        }

        /// <summary>
        /// الحصول على موقع اللاعب المحلي الحالي
        /// </summary>
        private static bool GetLocalPlayerPosition(out Vector3 position)
        {
            position = Vector3.Zero;

            try
            {
                if (!InternalMemory.Read(Core.LocalPlayer + (uint)Bones.Root, out uint rootBonePtr) || rootBonePtr == 0)
                    return false;

                if (!InternalMemory.Read(rootBonePtr + 0x8, out uint transform1) || transform1 == 0)
                    return false;

                if (!InternalMemory.Read(transform1 + 0x8, out uint transform2) || transform2 == 0)
                    return false;

                if (!InternalMemory.Read(transform2 + 0x20, out uint matrixPtr) || matrixPtr == 0)
                    return false;

                if (!InternalMemory.Read(matrixPtr + 0x80, out position))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تعيين موقع اللاعب المحلي
        /// </summary>
        private static bool SetLocalPlayerPosition(Vector3 newPosition)
        {
            try
            {
                if (!InternalMemory.Read(Core.LocalPlayer + (uint)Bones.Root, out uint rootBonePtr) || rootBonePtr == 0)
                    return false;

                if (!InternalMemory.Read(rootBonePtr + 0x8, out uint transform1) || transform1 == 0)
                    return false;

                if (!InternalMemory.Read(transform1 + 0x8, out uint transform2) || transform2 == 0)
                    return false;

                if (!InternalMemory.Read(transform2 + 0x20, out uint matrixPtr) || matrixPtr == 0)
                    return false;

                return InternalMemory.Write(matrixPtr + 0x80, newPosition);
            }
            catch
            {
                return false;
            }
        }
    }
}
