﻿
using System.Numerics;

namespace AotForms
{
    internal class Entity
    {
        internal bool IsKnown;
        internal Bool3 IsTeam;
        internal Vector3 Head;
        internal Vector3 LeftWrist;
        internal Vector3 RightWrist;
        internal Vector3 Spine;
        internal Vector3 Root;
        internal Vector3 Hip;
        internal Vector3 RightCalf;
        internal Vector3 LeftCalf;
        internal Vector3 RightFoot;
        internal Vector3 LeftFoot;
        internal Vector3 RighFoot;
        internal Vector3 LeftHand;
        internal Vector3 LeftSholder;
        internal Vector3 RightSholder;
        internal Vector3 RightWristJoint;
        internal Vector3 LeftWristJoint;
        internal Vector3 RightElbow;
        internal Vector3 LeftElbow;
        internal short Health;
        internal bool IsDead;
        internal bool IsKnocked;
        internal string Name;
        internal float Distance;
        internal bool IsFiring;
        internal bool isVisible;
        internal uint Address;
        internal uint Avatar;
        internal Vector3 Neck; // Posizione del collo
        internal Vector3 Pelvis; // Posizione del bacino
        internal Vector3 ShoulderR; // Posizione della spalla destra
        internal Vector3 ShoulderL; // Posizione della spalla sinistra
        internal Vector3 ElbowR; // Posizione del gomito destro
        internal Vector3 ElbowL; // Posizione del gomito sinistro
        internal Vector3 HandR; // Posizione della mano destra
        internal Vector3 HandL; // Posizione della mano sinistra
        internal Vector3 FootR; // Posizione del piede destro
        internal Vector3 FootL; // Posizione del piede sinistro
        internal Vector3 KneeL; // Posizione del ginocchio sinistro
        internal Vector3 KneeR; // Posizione del ginocchio destro
  
        internal string WeaponName;
       

        public bool Status { get; set; } // Aggiunto                  // ... (altri campi esistenti)

        public string WeaponStatus { get; set; } = "";

        // ... (mantieni tutte le altre proprietà esistenti)
        public IntPtr BaseAddress { get; set; }

        public string WeaponIconPath { get; set; }


        public Vector3 Position { get; set; } // Posizione dell'entità
    }
}