﻿using AotForms;

using Guna.UI2.WinForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Client
{
    public partial class EspForm : Form
    {
        public EspForm()
        {
            InitializeComponent();

        }

        private void guna2ToggleSwitch1_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.IgnoreKnocked = guna2ToggleSwitch1.Checked;
            Config.ESPLine = guna2ToggleSwitch1.Checked;
        }

        private void EspForm_Load(object sender, EventArgs e)
        {
            this.TopMost = false;

        }

        private void guna2ToggleSwitch2_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPBox = guna2ToggleSwitch2.Checked;
        }

        private void guna2ToggleSwitch3_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPFillBox = guna2ToggleSwitch3.Checked;
        }



        private void guna2ToggleSwitch5_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPSkeleton = guna2ToggleSwitch5.Checked;

        }

        private void guna2ToggleSwitch6_CheckedChanged(object sender, EventArgs e)
        {
            Config.Notif();
            Config.ESPName = guna2ToggleSwitch6.Checked;
        }



        private void guna2Button1_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button1.FillColor = picker.Color;
                Config.ESPLineColor = picker.Color;
            }
        }

        private void guna2Button2_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button2.FillColor = picker.Color;

                Config.ESPBoxColor = picker.Color;

            }
        }


        private void guna2Button3_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button3.FillColor = picker.Color;
                Config.ESPFillBoxColor = picker.Color;
            }
        }

        private void guna2Button4_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button4.FillColor = picker.Color;
                Config.ESPSkeletonColor = picker.Color;
            }
        }

        private void guna2PictureBox6_Click(object sender, EventArgs e)
        {

        }



        private void guna2PictureBox8_Click(object sender, EventArgs e)
        {

        }

        private void guna2Button5_Click(object sender, EventArgs e)
        {
            Config.Notif();
            var picker = new ColorDialog();
            var result = picker.ShowDialog();

            if (result == DialogResult.OK)
            {
                guna2Button5.BackColor = picker.Color;
                Config.ESPNameColor = picker.Color;
            }
        }

        private void guna2PictureBox2_Click(object sender, EventArgs e)
        {

        }

        private void guna2PictureBox4_Click(object sender, EventArgs e)
        {

        }
    }
}
