﻿namespace Client
{
    partial class EspForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges27 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges28 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges29 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges30 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges23 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges24 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges25 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges26 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges19 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges20 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges21 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges22 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges15 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges16 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges17 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges18 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges13 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges14 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges11 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges12 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges9 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges10 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges7 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges8 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges3 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges4 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges5 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges6 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges1 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges2 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EspForm));
            guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(components);
            guna2ToggleSwitch2 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            label2 = new Label();
            guna2PictureBox3 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2ToggleSwitch3 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            label3 = new Label();
            guna2PictureBox4 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2ToggleSwitch5 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            label5 = new Label();
            guna2PictureBox6 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2ToggleSwitch6 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            label6 = new Label();
            guna2PictureBox7 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2Button2 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button3 = new Guna.UI2.WinForms.Guna2Button();
            guna2Button4 = new Guna.UI2.WinForms.Guna2Button();
            guna2DragControl1 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2DragControl3 = new Guna.UI2.WinForms.Guna2DragControl(components);
            guna2Button5 = new Guna.UI2.WinForms.Guna2Button();
            guna2ToggleSwitch1 = new Guna.UI2.WinForms.Guna2ToggleSwitch();
            label1 = new Label();
            guna2PictureBox2 = new Guna.UI2.WinForms.Guna2PictureBox();
            guna2Button1 = new Guna.UI2.WinForms.Guna2Button();
            pictureBox1 = new PictureBox();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox3).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox4).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox6).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox7).BeginInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            SuspendLayout();
            // 
            // guna2BorderlessForm1
            // 
            guna2BorderlessForm1.BorderRadius = 20;
            guna2BorderlessForm1.ContainerControl = this;
            guna2BorderlessForm1.DockForm = false;
            guna2BorderlessForm1.DockIndicatorTransparencyValue = 1D;
            guna2BorderlessForm1.DragStartTransparencyValue = 1D;
            guna2BorderlessForm1.HasFormShadow = false;
            guna2BorderlessForm1.ResizeForm = false;
            guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // guna2ToggleSwitch2
            // 
            guna2ToggleSwitch2.Animated = true;
            guna2ToggleSwitch2.AutoRoundedCorners = true;
            guna2ToggleSwitch2.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch2.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch2.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch2.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch2.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch2.CustomizableEdges = customizableEdges27;
            guna2ToggleSwitch2.Location = new Point(186, 119);
            guna2ToggleSwitch2.Name = "guna2ToggleSwitch2";
            guna2ToggleSwitch2.ShadowDecoration.CustomizableEdges = customizableEdges28;
            guna2ToggleSwitch2.Size = new Size(43, 21);
            guna2ToggleSwitch2.TabIndex = 45;
            guna2ToggleSwitch2.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch2.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch2.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch2.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch2.CheckedChanged += guna2ToggleSwitch2_CheckedChanged;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.BackColor = Color.FromArgb(21, 21, 21);
            label2.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label2.ForeColor = Color.Silver;
            label2.Location = new Point(20, 123);
            label2.Name = "label2";
            label2.Size = new Size(57, 15);
            label2.TabIndex = 43;
            label2.Text = "ESP BOX";
            // 
            // guna2PictureBox3
            // 
            guna2PictureBox3.BorderRadius = 5;
            guna2PictureBox3.CustomizableEdges = customizableEdges29;
            guna2PictureBox3.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox3.ImageRotate = 0F;
            guna2PictureBox3.Location = new Point(10, 109);
            guna2PictureBox3.Name = "guna2PictureBox3";
            guna2PictureBox3.ShadowDecoration.CustomizableEdges = customizableEdges30;
            guna2PictureBox3.Size = new Size(263, 42);
            guna2PictureBox3.TabIndex = 44;
            guna2PictureBox3.TabStop = false;
            // 
            // guna2ToggleSwitch3
            // 
            guna2ToggleSwitch3.Animated = true;
            guna2ToggleSwitch3.AutoRoundedCorners = true;
            guna2ToggleSwitch3.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch3.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch3.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch3.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch3.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch3.CustomizableEdges = customizableEdges23;
            guna2ToggleSwitch3.Location = new Point(186, 168);
            guna2ToggleSwitch3.Name = "guna2ToggleSwitch3";
            guna2ToggleSwitch3.ShadowDecoration.CustomizableEdges = customizableEdges24;
            guna2ToggleSwitch3.Size = new Size(43, 21);
            guna2ToggleSwitch3.TabIndex = 48;
            guna2ToggleSwitch3.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch3.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch3.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch3.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch3.CheckedChanged += guna2ToggleSwitch3_CheckedChanged;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.BackColor = Color.FromArgb(21, 21, 21);
            label3.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label3.ForeColor = Color.Silver;
            label3.Location = new Point(20, 171);
            label3.Name = "label3";
            label3.Size = new Size(82, 15);
            label3.TabIndex = 46;
            label3.Text = "ESP FILL BOX";
            // 
            // guna2PictureBox4
            // 
            guna2PictureBox4.BorderRadius = 5;
            guna2PictureBox4.CustomizableEdges = customizableEdges25;
            guna2PictureBox4.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox4.ImageRotate = 0F;
            guna2PictureBox4.Location = new Point(10, 157);
            guna2PictureBox4.Name = "guna2PictureBox4";
            guna2PictureBox4.ShadowDecoration.CustomizableEdges = customizableEdges26;
            guna2PictureBox4.Size = new Size(263, 42);
            guna2PictureBox4.TabIndex = 47;
            guna2PictureBox4.TabStop = false;
            guna2PictureBox4.Click += guna2PictureBox4_Click;
            // 
            // guna2ToggleSwitch5
            // 
            guna2ToggleSwitch5.Animated = true;
            guna2ToggleSwitch5.AutoRoundedCorners = true;
            guna2ToggleSwitch5.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch5.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch5.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch5.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch5.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch5.CustomizableEdges = customizableEdges19;
            guna2ToggleSwitch5.Location = new Point(186, 213);
            guna2ToggleSwitch5.Name = "guna2ToggleSwitch5";
            guna2ToggleSwitch5.ShadowDecoration.CustomizableEdges = customizableEdges20;
            guna2ToggleSwitch5.Size = new Size(43, 21);
            guna2ToggleSwitch5.TabIndex = 54;
            guna2ToggleSwitch5.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch5.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch5.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch5.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch5.CheckedChanged += guna2ToggleSwitch5_CheckedChanged;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.BackColor = Color.FromArgb(21, 21, 21);
            label5.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label5.ForeColor = Color.Silver;
            label5.Location = new Point(20, 219);
            label5.Name = "label5";
            label5.Size = new Size(94, 15);
            label5.TabIndex = 52;
            label5.Text = "ESP SKELETON";
            // 
            // guna2PictureBox6
            // 
            guna2PictureBox6.BorderRadius = 5;
            guna2PictureBox6.CustomizableEdges = customizableEdges21;
            guna2PictureBox6.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox6.ImageRotate = 0F;
            guna2PictureBox6.Location = new Point(10, 205);
            guna2PictureBox6.Name = "guna2PictureBox6";
            guna2PictureBox6.ShadowDecoration.CustomizableEdges = customizableEdges22;
            guna2PictureBox6.Size = new Size(263, 42);
            guna2PictureBox6.TabIndex = 53;
            guna2PictureBox6.TabStop = false;
            guna2PictureBox6.Click += guna2PictureBox6_Click;
            // 
            // guna2ToggleSwitch6
            // 
            guna2ToggleSwitch6.Animated = true;
            guna2ToggleSwitch6.AutoRoundedCorners = true;
            guna2ToggleSwitch6.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch6.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch6.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch6.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch6.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch6.CustomizableEdges = customizableEdges15;
            guna2ToggleSwitch6.Location = new Point(186, 261);
            guna2ToggleSwitch6.Name = "guna2ToggleSwitch6";
            guna2ToggleSwitch6.ShadowDecoration.CustomizableEdges = customizableEdges16;
            guna2ToggleSwitch6.Size = new Size(43, 21);
            guna2ToggleSwitch6.TabIndex = 57;
            guna2ToggleSwitch6.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch6.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch6.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch6.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch6.CheckedChanged += guna2ToggleSwitch6_CheckedChanged;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.BackColor = Color.FromArgb(21, 21, 21);
            label6.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label6.ForeColor = Color.Silver;
            label6.Location = new Point(20, 267);
            label6.Name = "label6";
            label6.Size = new Size(121, 15);
            label6.TabIndex = 55;
            label6.Text = "ESP INFORMATION";
            // 
            // guna2PictureBox7
            // 
            guna2PictureBox7.BorderRadius = 5;
            guna2PictureBox7.CustomizableEdges = customizableEdges17;
            guna2PictureBox7.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox7.ImageRotate = 0F;
            guna2PictureBox7.Location = new Point(10, 253);
            guna2PictureBox7.Name = "guna2PictureBox7";
            guna2PictureBox7.ShadowDecoration.CustomizableEdges = customizableEdges18;
            guna2PictureBox7.Size = new Size(263, 42);
            guna2PictureBox7.TabIndex = 56;
            guna2PictureBox7.TabStop = false;
            // 
            // guna2Button2
            // 
            guna2Button2.Animated = true;
            guna2Button2.BackColor = Color.FromArgb(21, 21, 21);
            guna2Button2.BorderRadius = 5;
            guna2Button2.CustomizableEdges = customizableEdges13;
            guna2Button2.DisabledState.BorderColor = Color.DarkGray;
            guna2Button2.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button2.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button2.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button2.FillColor = Color.White;
            guna2Button2.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button2.ForeColor = Color.White;
            guna2Button2.Location = new Point(235, 116);
            guna2Button2.Name = "guna2Button2";
            guna2Button2.ShadowDecoration.CustomizableEdges = customizableEdges14;
            guna2Button2.Size = new Size(22, 22);
            guna2Button2.TabIndex = 61;
            guna2Button2.Click += guna2Button2_Click;
            // 
            // guna2Button3
            // 
            guna2Button3.Animated = true;
            guna2Button3.BackColor = Color.FromArgb(21, 21, 21);
            guna2Button3.BorderRadius = 5;
            guna2Button3.CustomizableEdges = customizableEdges11;
            guna2Button3.DisabledState.BorderColor = Color.DarkGray;
            guna2Button3.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button3.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button3.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button3.FillColor = Color.White;
            guna2Button3.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button3.ForeColor = Color.White;
            guna2Button3.Location = new Point(235, 164);
            guna2Button3.Name = "guna2Button3";
            guna2Button3.ShadowDecoration.CustomizableEdges = customizableEdges12;
            guna2Button3.Size = new Size(22, 22);
            guna2Button3.TabIndex = 63;
            guna2Button3.Click += guna2Button3_Click;
            // 
            // guna2Button4
            // 
            guna2Button4.Animated = true;
            guna2Button4.BackColor = Color.FromArgb(21, 21, 21);
            guna2Button4.BorderRadius = 5;
            guna2Button4.CustomizableEdges = customizableEdges9;
            guna2Button4.DisabledState.BorderColor = Color.DarkGray;
            guna2Button4.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button4.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button4.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button4.FillColor = Color.White;
            guna2Button4.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button4.ForeColor = Color.White;
            guna2Button4.Location = new Point(235, 212);
            guna2Button4.Name = "guna2Button4";
            guna2Button4.ShadowDecoration.CustomizableEdges = customizableEdges10;
            guna2Button4.Size = new Size(22, 22);
            guna2Button4.TabIndex = 65;
            guna2Button4.Click += guna2Button4_Click;
            // 
            // guna2DragControl1
            // 
            guna2DragControl1.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl1.TransparentWhileDrag = false;
            // 
            // guna2DragControl3
            // 
            guna2DragControl3.DockIndicatorTransparencyValue = 0.6D;
            guna2DragControl3.UseTransparentDrag = true;
            // 
            // guna2Button5
            // 
            guna2Button5.Animated = true;
            guna2Button5.BackColor = Color.FromArgb(21, 21, 21);
            guna2Button5.BorderRadius = 5;
            guna2Button5.CustomizableEdges = customizableEdges7;
            guna2Button5.DisabledState.BorderColor = Color.DarkGray;
            guna2Button5.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button5.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button5.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button5.FillColor = Color.White;
            guna2Button5.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button5.ForeColor = Color.White;
            guna2Button5.Location = new Point(235, 260);
            guna2Button5.Name = "guna2Button5";
            guna2Button5.ShadowDecoration.CustomizableEdges = customizableEdges8;
            guna2Button5.Size = new Size(22, 22);
            guna2Button5.TabIndex = 69;
            guna2Button5.Click += guna2Button5_Click;
            // 
            // guna2ToggleSwitch1
            // 
            guna2ToggleSwitch1.Animated = true;
            guna2ToggleSwitch1.AutoRoundedCorners = true;
            guna2ToggleSwitch1.BackColor = Color.FromArgb(21, 21, 21);
            guna2ToggleSwitch1.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            guna2ToggleSwitch1.CheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch1.CheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch1.CheckedState.InnerColor = Color.Red;
            guna2ToggleSwitch1.CustomizableEdges = customizableEdges3;
            guna2ToggleSwitch1.Location = new Point(186, 75);
            guna2ToggleSwitch1.Name = "guna2ToggleSwitch1";
            guna2ToggleSwitch1.ShadowDecoration.CustomizableEdges = customizableEdges4;
            guna2ToggleSwitch1.Size = new Size(40, 21);
            guna2ToggleSwitch1.TabIndex = 78;
            guna2ToggleSwitch1.UncheckedState.BorderColor = Color.FromArgb(6, 6, 6);
            guna2ToggleSwitch1.UncheckedState.FillColor = Color.FromArgb(10, 10, 10);
            guna2ToggleSwitch1.UncheckedState.InnerBorderColor = Color.White;
            guna2ToggleSwitch1.UncheckedState.InnerColor = Color.FromArgb(43, 43, 43);
            guna2ToggleSwitch1.CheckedChanged += guna2ToggleSwitch1_CheckedChanged;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.BackColor = Color.FromArgb(21, 21, 21);
            label1.Font = new Font("Segoe UI Black", 9F, FontStyle.Bold, GraphicsUnit.Point);
            label1.ForeColor = Color.Silver;
            label1.Location = new Point(20, 75);
            label1.Name = "label1";
            label1.Size = new Size(59, 15);
            label1.TabIndex = 76;
            label1.Text = "ESP LINE";
            // 
            // guna2PictureBox2
            // 
            guna2PictureBox2.BorderRadius = 5;
            guna2PictureBox2.CustomizableEdges = customizableEdges5;
            guna2PictureBox2.FillColor = Color.FromArgb(21, 21, 21);
            guna2PictureBox2.ImageRotate = 0F;
            guna2PictureBox2.Location = new Point(10, 61);
            guna2PictureBox2.Name = "guna2PictureBox2";
            guna2PictureBox2.ShadowDecoration.CustomizableEdges = customizableEdges6;
            guna2PictureBox2.Size = new Size(263, 42);
            guna2PictureBox2.TabIndex = 77;
            guna2PictureBox2.TabStop = false;
            guna2PictureBox2.Click += guna2PictureBox2_Click;
            // 
            // guna2Button1
            // 
            guna2Button1.Animated = true;
            guna2Button1.BackColor = Color.FromArgb(21, 21, 21);
            guna2Button1.BorderRadius = 5;
            guna2Button1.CustomizableEdges = customizableEdges1;
            guna2Button1.DisabledState.BorderColor = Color.DarkGray;
            guna2Button1.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2Button1.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            guna2Button1.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2Button1.FillColor = Color.White;
            guna2Button1.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2Button1.ForeColor = Color.White;
            guna2Button1.Location = new Point(235, 74);
            guna2Button1.Name = "guna2Button1";
            guna2Button1.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2Button1.Size = new Size(22, 22);
            guna2Button1.TabIndex = 80;
            guna2Button1.Click += guna2Button1_Click;
            // 
            // pictureBox1
            // 
            pictureBox1.Dock = DockStyle.Top;
            pictureBox1.Image = (Image)resources.GetObject("pictureBox1.Image");
            pictureBox1.Location = new Point(0, 0);
            pictureBox1.Margin = new Padding(4, 3, 4, 3);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(285, 52);
            pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;
            pictureBox1.TabIndex = 81;
            pictureBox1.TabStop = false;
            // 
            // EspForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(8, 8, 8);
            BackgroundImageLayout = ImageLayout.Stretch;
            ClientSize = new Size(285, 304);
            Controls.Add(pictureBox1);
            Controls.Add(guna2Button1);
            Controls.Add(guna2ToggleSwitch1);
            Controls.Add(label1);
            Controls.Add(guna2PictureBox2);
            Controls.Add(guna2Button5);
            Controls.Add(guna2Button4);
            Controls.Add(guna2Button3);
            Controls.Add(guna2Button2);
            Controls.Add(guna2ToggleSwitch6);
            Controls.Add(label6);
            Controls.Add(guna2PictureBox7);
            Controls.Add(guna2ToggleSwitch5);
            Controls.Add(label5);
            Controls.Add(guna2PictureBox6);
            Controls.Add(guna2ToggleSwitch3);
            Controls.Add(label3);
            Controls.Add(guna2PictureBox4);
            Controls.Add(guna2ToggleSwitch2);
            Controls.Add(label2);
            Controls.Add(guna2PictureBox3);
            DoubleBuffered = true;
            FormBorderStyle = FormBorderStyle.None;
            Name = "EspForm";
            ShowIcon = false;
            ShowInTaskbar = false;
            Text = "ESP MENU";
            Load += EspForm_Load;
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox3).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox4).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox6).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox7).EndInit();
            ((System.ComponentModel.ISupportInitialize)guna2PictureBox2).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Guna.UI2.WinForms.Guna2BorderlessForm guna2BorderlessForm1;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch6;
        private Label label6;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox7;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch5;
        private Label label5;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox6;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch3;
        private Label label3;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox4;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch2;
        private Label label2;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox3;
        private Guna.UI2.WinForms.Guna2Button guna2Button4;
        private Guna.UI2.WinForms.Guna2Button guna2Button3;
        private Guna.UI2.WinForms.Guna2Button guna2Button2;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl1;
        private Guna.UI2.WinForms.Guna2DragControl guna2DragControl3;
        private Guna.UI2.WinForms.Guna2Button guna2Button5;
        private Guna.UI2.WinForms.Guna2Button guna2Button1;
        private Guna.UI2.WinForms.Guna2ToggleSwitch guna2ToggleSwitch1;
        private Label label1;
        private Guna.UI2.WinForms.Guna2PictureBox guna2PictureBox2;
        private PictureBox pictureBox1;
    }
}