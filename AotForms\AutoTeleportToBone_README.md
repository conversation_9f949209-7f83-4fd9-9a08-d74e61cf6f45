# نظام التحريك التلقائي نحو العظام (Auto Teleport to Bone)

## الوصف
هذا النظام يقوم تلقائياً بالبحث عن أقرب عدو صالح (حي ومعروف) ثم يجد أقرب عظمة لهذا العدو ويحرك اللاعب المحلي تدريجياً نحو هذه العظمة بحركة سلسة وقابلة للتحكم.

## الملفات المضافة

### 1. AutoTeleportToBone.cs
الملف الرئيسي الذي يحتوي على منطق النظام:
- `AutoTeleportLoop()`: الحلقة الرئيسية للتحريك
- `FindClosestValidEnemy()`: البحث عن أقرب عدو صالح
- `FindClosestBonePosition()`: البحث عن أقرب عظمة
- `TeleportToBone()`: التحريك التدريجي نحو الهدف

### 2. AutoTeleportConfig.cs
واجهة المستخدم لإعداد النظام:
- نافذة إعدادات مع أشرطة تمرير للتحكم
- أزرار التشغيل والإيقاف
- عرض الحالة الحالية

### 3. AutoTeleportExample.cs
أمثلة على كيفية استخدام النظام:
- أمثلة بسيطة ومتقدمة
- إعدادات مختلفة للأوضاع المختلفة
- واجهة مستخدم للتحكم

## كيفية الاستخدام

### الاستخدام البسيط
```csharp
// تفعيل النظام بالإعدادات الافتراضية
AutoTeleportToBone.IsEnabled = true;
AutoTeleportToBone.Start();
```

### الاستخدام المتقدم
```csharp
// تخصيص الإعدادات
AutoTeleportToBone.TeleportSpeed = 0.1f;        // سرعة التحريك (0.01-1.0)
AutoTeleportToBone.MinDistanceToTarget = 2.0f;  // المسافة الدنيا للتوقف
AutoTeleportToBone.UpdateDelayMs = 50;          // تأخير التحديث بالميلي ثانية

// تشغيل النظام
AutoTeleportToBone.IsEnabled = true;
AutoTeleportToBone.Start();
```

### استخدام واجهة الإعدادات
```csharp
// فتح نافذة الإعدادات
AutoTeleportManager.ShowConfigWindow();

// تشغيل/إيقاف سريع
AutoTeleportManager.Toggle();
```

## الإعدادات المتاحة

### TeleportSpeed (سرعة التحريك)
- **النطاق**: 0.01 - 1.0
- **الافتراضي**: 0.1
- **الوصف**: يحدد سرعة التحريك نحو الهدف. القيم الأقل = حركة أبطأ وأكثر سلاسة

### MinDistanceToTarget (المسافة الدنيا للتوقف)
- **النطاق**: 0.1 - 10.0 متر
- **الافتراضي**: 2.0 متر
- **الوصف**: المسافة التي يتوقف عندها النظام عن التحريك

### UpdateDelayMs (تأخير التحديث)
- **النطاق**: 10 - 500 ميلي ثانية
- **الافتراضي**: 50 ميلي ثانية
- **الوصف**: الفترة الزمنية بين كل تحديث للموقع

## العظام المستهدفة
النظام يبحث في العظام التالية لإيجاد أقرب نقطة:
- الرأس (Head)
- الرقبة (Neck)
- الحوض (Pelvis)
- الأكتاف (ShoulderL, ShoulderR)
- المرفقين (ElbowL, ElbowR)
- اليدين (HandL, HandR)
- القدمين (FootL, FootR)
- الركبتين (KneeL, KneeR)
- الورك (Hip)
- العمود الفقري (Spine)

## ميزات الأمان

### تجنب الانهيار
- فحص صحة المؤشرات قبل القراءة/الكتابة
- معالجة الأخطاء في جميع العمليات
- تجاهل الأعداء غير الصالحين

### فلترة الأعداء
- تجاهل الأعداء الموتى (`IsDead = true`)
- تجاهل الأعداء المصابين (`IsKnocked = true`)
- تجاهل الأعداء غير المعروفين (`IsKnown = false`)
- فحص صحة عناوين الذاكرة

## أمثلة الإعدادات

### الوضع الدفاعي (بطيء وحذر)
```csharp
AutoTeleportToBone.TeleportSpeed = 0.03f;
AutoTeleportToBone.MinDistanceToTarget = 5.0f;
AutoTeleportToBone.UpdateDelayMs = 100;
```

### الوضع الهجومي (سريع وعدواني)
```csharp
AutoTeleportToBone.TeleportSpeed = 0.3f;
AutoTeleportToBone.MinDistanceToTarget = 1.0f;
AutoTeleportToBone.UpdateDelayMs = 20;
```

### الوضع المتوازن
```csharp
AutoTeleportToBone.TeleportSpeed = 0.1f;
AutoTeleportToBone.MinDistanceToTarget = 2.5f;
AutoTeleportToBone.UpdateDelayMs = 50;
```

## التحكم عبر المفاتيح
يمكن ربط النظام بمفاتيح الكيبورد:
```csharp
// F1 - تشغيل/إيقاف
if (keyPressed == Keys.F1)
    AutoTeleportManager.Toggle();

// F2 - فتح الإعدادات
if (keyPressed == Keys.F2)
    AutoTeleportManager.ShowConfigWindow();
```

## إيقاف النظام بأمان
```csharp
// إيقاف النظام
AutoTeleportToBone.Stop();

// انتظار للتأكد من الإيقاف
await Task.Delay(500);
```

## ملاحظات مهمة

1. **الأداء**: النظام مصمم ليكون خفيف على الأداء مع إعدادات التأخير المناسبة
2. **الأمان**: جميع العمليات محمية ضد الأخطاء لتجنب انهيار البرنامج
3. **المرونة**: يمكن تخصيص جميع الإعدادات حسب الحاجة
4. **التوافق**: يعمل مع البنية الحالية للمشروع دون تعديلات

## استكشاف الأخطاء

### النظام لا يتحرك
- تأكد من تفعيل النظام (`IsEnabled = true`)
- تحقق من وجود أعداء صالحين
- تأكد من صحة قراءة موقع اللاعب المحلي

### الحركة سريعة جداً أو بطيئة جداً
- اضبط قيمة `TeleportSpeed`
- اضبط قيمة `UpdateDelayMs`

### النظام يتوقف فجأة
- تحقق من قيمة `MinDistanceToTarget`
- تأكد من عدم موت العدو المستهدف
