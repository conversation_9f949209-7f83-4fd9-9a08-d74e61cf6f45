using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace AotForms
{
    /// <summary>
    /// Performance testing and validation class
    /// </summary>
    internal static class PerformanceTests
    {
        /// <summary>
        /// Run comprehensive performance tests
        /// </summary>
        public static async Task RunAllTests()
        {
            ErrorHandler.LogInfo("Starting performance tests...");

            try
            {
                await TestMemoryManagement();
                await TestThreadingPerformance();
                await TestErrorHandling();
                //await TestCachePerformance();
                TestConfigurationValidation();

                ErrorHandler.LogInfo("All performance tests completed successfully");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Performance tests failed", ex);
            }
        }

        /// <summary>
        /// Test memory management and garbage collection
        /// </summary>
        private static async Task TestMemoryManagement()
        {
            ErrorHandler.LogInfo("Testing memory management...");

            var initialMemory = GC.GetTotalMemory(false);
            
            // Simulate memory intensive operations
            var testData = new byte[1024 * 1024]; // 1MB
            for (int i = 0; i < 100; i++)
            {
                var tempData = new byte[10240]; // 10KB
                Array.Copy(testData, tempData, Math.Min(testData.Length, tempData.Length));
                
                if (i % 10 == 0)
                {
                    await Task.Delay(1);
                }
            }

            // Force garbage collection
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var finalMemory = GC.GetTotalMemory(true);
            var memoryDiff = finalMemory - initialMemory;

            ErrorHandler.LogInfo($"Memory test completed. Initial: {initialMemory / 1024}KB, " +
                $"Final: {finalMemory / 1024}KB, Diff: {memoryDiff / 1024}KB");

            // Memory should not have increased significantly
            if (memoryDiff > 5 * 1024 * 1024) // 5MB threshold
            {
                ErrorHandler.LogWarning($"Memory usage increased by {memoryDiff / 1024 / 1024}MB - possible memory leak");
            }
        }

        /// <summary>
        /// Test threading performance and cancellation
        /// </summary>
        private static async Task TestThreadingPerformance()
        {
            ErrorHandler.LogInfo("Testing threading performance...");

            var cancellationTokenSource = new CancellationTokenSource();
            var stopwatch = Stopwatch.StartNew();

            // Test multiple concurrent tasks
            var tasks = new Task[10];
            for (int i = 0; i < tasks.Length; i++)
            {
                int taskId = i;
                tasks[i] = Task.Run(async () =>
                {
                    try
                    {
                        for (int j = 0; j < 100 && !cancellationTokenSource.Token.IsCancellationRequested; j++)
                        {
                            await Task.Delay(1, cancellationTokenSource.Token);
                            
                            // Simulate some work
                            var result = Math.Sqrt(j * taskId + 1);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected when cancelled
                    }
                }, cancellationTokenSource.Token);
            }

            // Let tasks run for a short time
            await Task.Delay(500);

            // Cancel all tasks
            cancellationTokenSource.Cancel();

            // Wait for all tasks to complete
            try
            {
                await Task.WhenAll(tasks);
            }
            catch (OperationCanceledException)
            {
                // Expected
            }

            stopwatch.Stop();

            ErrorHandler.LogInfo($"Threading test completed in {stopwatch.ElapsedMilliseconds}ms. " +
                $"All {tasks.Length} tasks cancelled successfully");

            cancellationTokenSource.Dispose();
        }

        /// <summary>
        /// Test error handling and recovery
        /// </summary>
        private static async Task TestErrorHandling()
        {
            ErrorHandler.LogInfo("Testing error handling...");

            var errorCount = 0;

            // Test various error scenarios
            for (int i = 0; i < 5; i++)
            {
                try
                {
                    switch (i)
                    {
                        case 0:
                            throw new InvalidOperationException("Test invalid operation");
                        case 1:
                            throw new ArgumentNullException("testParam", "Test null argument");
                        case 2:
                            throw new OutOfMemoryException("Test memory exception");
                        case 3:
                            var array = new int[1];
                            _ = array[10]; // Index out of range
                            break;
                        case 4:
                            await Task.Run(() => throw new TaskCanceledException("Test task cancellation"));
                            break;
                    }
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError($"Test error {i + 1}", ex);
                    errorCount++;
                }

                await Task.Delay(10);
            }

            ErrorHandler.LogInfo($"Error handling test completed. Handled {errorCount} errors successfully");
        }

        /// <summary>
        /// Test cache performance and limits
        /// </summary>
        //private static async Task TestCachePerformance()
        //{
        //    ErrorHandler.LogInfo("Testing cache performance...");

        //    var stopwatch = Stopwatch.StartNew();
        //    var cacheHits = 0;
        //    var cacheMisses = 0;

        //    // Test cache operations
        //    for (ulong i = 0; i < 1000; i++)
        //    {
        //        var address = 0x1000000 + i * 4;
                
        //        // First access should be a miss
        //        if (InternalMemory.Cache.ContainsKey(address))
        //        {
        //            cacheHits++;
        //        }
        //        else
        //        {
        //            cacheMisses++;
        //            // Simulate adding to cache
        //            if (InternalMemory.Cache.Count < PerformanceConfig.MaxCacheSize)
        //            {
        //                InternalMemory.Cache.TryAdd(address, address + 0x10000);
        //            }
        //        }

        //        // Second access should be a hit
        //        if (InternalMemory.Cache.ContainsKey(address))
        //        {
        //            cacheHits++;
        //        }

        //        if (i % 100 == 0)
        //        {
        //            await Task.Delay(1);
        //        }
        //    }

        //    stopwatch.Stop();

        //    var cacheSize = InternalMemory.Cache.Count;
        //    var hitRate = cacheHits > 0 ? (double)cacheHits / (cacheHits + cacheMisses) * 100 : 0;

        //    ErrorHandler.LogInfo($"Cache test completed in {stopwatch.ElapsedMilliseconds}ms. " +
        //        $"Size: {cacheSize}, Hits: {cacheHits}, Misses: {cacheMisses}, Hit Rate: {hitRate:F1}%");

        //    // Clean up test cache entries
        //    InternalMemory.ClearCache();
        //}

        /// <summary>
        /// Test configuration validation
        /// </summary>
        private static void TestConfigurationValidation()
        {
            ErrorHandler.LogInfo("Testing configuration validation...");

            // Save current settings
            var originalAimbotFPS = PerformanceConfig.AimbotTargetFPS;
            var originalMaxEntities = PerformanceConfig.MaxEntitiesPerFrame;

            try
            {
                // Test invalid values
                PerformanceConfig.AimbotTargetFPS = -10;
                PerformanceConfig.MaxEntitiesPerFrame = 0;

                // Validate should fix these
                PerformanceConfig.ValidateSettings();

                var isValid = PerformanceConfig.AimbotTargetFPS > 0 && PerformanceConfig.MaxEntitiesPerFrame > 0;

                ErrorHandler.LogInfo($"Configuration validation test: {(isValid ? "PASSED" : "FAILED")}");
                ErrorHandler.LogInfo($"Fixed values - AimbotFPS: {PerformanceConfig.AimbotTargetFPS}, " +
                    $"MaxEntities: {PerformanceConfig.MaxEntitiesPerFrame}");
            }
            finally
            {
                // Restore original settings
                PerformanceConfig.AimbotTargetFPS = originalAimbotFPS;
                PerformanceConfig.MaxEntitiesPerFrame = originalMaxEntities;
            }
        }

        /// <summary>
        /// Benchmark specific operations
        /// </summary>
        public static async Task BenchmarkOperations()
        {
            ErrorHandler.LogInfo("Starting operation benchmarks...");

            // Benchmark frame rate limiting
            await BenchmarkFrameRateLimiting();

            // Benchmark entity processing
            await BenchmarkEntityProcessing();

            ErrorHandler.LogInfo("Operation benchmarks completed");
        }

        private static async Task BenchmarkFrameRateLimiting()
        {
            var stopwatch = Stopwatch.StartNew();
            var frameCount = 0;
            var targetFPS = 60;

            while (stopwatch.ElapsedMilliseconds < 1000) // Run for 1 second
            {
                await FrameRateLimiter.LimitFrameRate(targetFPS);
                frameCount++;
            }

            var actualFPS = FrameRateLimiter.GetCurrentFps();
            var efficiency = Math.Abs(actualFPS - targetFPS) / targetFPS * 100;

            ErrorHandler.LogInfo($"Frame rate benchmark: Target={targetFPS}fps, Actual={actualFPS:F1}fps, " +
                $"Frames={frameCount}, Efficiency={100 - efficiency:F1}%");
        }

        private static async Task BenchmarkEntityProcessing()
        {
            var stopwatch = Stopwatch.StartNew();
            var entitiesProcessed = 0;
            var maxEntities = PerformanceConfig.MaxEntitiesPerFrame;

            // Simulate entity processing
            for (int frame = 0; frame < 60; frame++) // 60 frames
            {
                for (int entity = 0; entity < maxEntities; entity++)
                {
                    // Simulate entity processing work
                    var distance = Math.Sqrt(entity * frame + 1);
                    entitiesProcessed++;
                }

                await Task.Delay(1); // Simulate frame delay
            }

            stopwatch.Stop();

            var entitiesPerSecond = entitiesProcessed / (stopwatch.ElapsedMilliseconds / 1000.0);

            ErrorHandler.LogInfo($"Entity processing benchmark: {entitiesProcessed} entities in {stopwatch.ElapsedMilliseconds}ms, " +
                $"Rate: {entitiesPerSecond:F0} entities/second");
        }
    }
}
