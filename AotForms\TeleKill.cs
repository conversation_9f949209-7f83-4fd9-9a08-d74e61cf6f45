﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AotForms;

namespace Client
{
    internal static class ProxTelekill
    {
        private static Task upPlayerTask;
        private static CancellationTokenSource cts = new();
        private static bool isRunning = false;

        internal static void Work()
        {
            if (isRunning) return;
            isRunning = true;

            upPlayerTask = Task.Run(async () =>
            {
                while (!cts.Token.IsCancellationRequested)
                {
                    if (!Config.proxtelekill)
                    {
                        await Task.Delay(1, cts.Token);
                        continue;
                    }

                    Entity closestEntity = null;
                    float closestDistance = float.MaxValue;

                    foreach (var entity in Core.Entities.Values)
                    {
                        if (!entity.IsKnown || entity.IsDead || (Config.IgnoreKnocked && entity.IsKnocked))
                            continue;

                        float distance = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                        if (distance < closestDistance)
                        {
                            closestDistance = distance;
                            closestEntity = entity;
                        }
                    }

                    if (closestEntity != null)
                    {
                      
                        Bones[] targetBones = new Bones[]
                        {
                    <PERSON>,
                    <PERSON>,
                    <PERSON>,
                    Bones.<PERSON>,
                    <PERSON>,
                    <PERSON>.<PERSON>,
                    <PERSON>.HandR,
                    Bones.ElbowL,
                    Bones.ElbowR,
                    Bones.ShoulderL,
                    Bones.ShoulderR
                        };

                        float minDistance = float.MaxValue;
                        Vector3 closestBonePosition = Vector3.Zero;

                        foreach (var bone in targetBones)
                        {
                            var bonePtr = InternalMemory.Read<uint>(closestEntity.Address + (uint)bone, out var boneBase);
                            if (boneBase == 0) continue;

                            var boneTransform = Transform.GetNodePosition(boneBase, out var position);
                            float distance = Vector3.Distance(Core.LocalMainCamera, position);

                            if (distance < minDistance)
                            {
                                minDistance = distance;
                                closestBonePosition = position;
                            }
                        }

                        var localRootBone = InternalMemory.Read<uint>(Core.LocalPlayer + (uint)Bones.Root, out var localRootBonePtr);
                        var localTransform = InternalMemory.Read<uint>(localRootBonePtr + 0x8, out var localTransformValue);
                        var localTransformObj = InternalMemory.Read<uint>(localTransformValue + 0x8, out var localTransformObjPtr);
                        var localMatrix = InternalMemory.Read<uint>(localTransformObjPtr + 0x20, out var localMatrixValue);

                        
                        InternalMemory.Write<Vector3>(localMatrixValue + 0x80, closestBonePosition);
                    }

                    await Task.Delay(1, cts.Token);
                }
            }, cts.Token);
        }

        internal static void Stop()
        {
            if (!isRunning) return;

            cts.Cancel();
            isRunning = false;
        }
    }
}
