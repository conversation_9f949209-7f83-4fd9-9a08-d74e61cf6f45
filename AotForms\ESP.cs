﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using ImGuiNET;
using System.Numerics;
using System.Drawing;
using System.Collections.Generic;

namespace AotForms
{
    internal class ESP : ClickableTransparentOverlay.Overlay
    {
        private IntPtr hWnd;
        private IntPtr HDPlayer;
        private const short DefaultMaxHealth = 200;
        public float GlowIntensitySkeleton { get; set; } = 1.0f;
        public float GlowIntensityName { get; set; } = 1.0f;
        public float GlowIntensityBox { get; set; } = 1.0f;

        [DllImport("kernel32.dll")]
        public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

        [DllImport("kernel32.dll")]
        public static extern IntPtr OpenProcess(int dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("user32.dll", SetLastError = true)]
        public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll", SetLastError = true)]
        public static extern int GetWindowLong(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll")]
        public static extern int SetWindowLong(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        const int PROCESS_VM_READ = 0x0010;
        private const int WEAPON_NAME_OFFSET = 0x38;
        private const int MAX_WEAPON_NAME_LENGTH = 64;
        private const int GWL_EXSTYLE = -20;
        private const int WS_EX_TOOLWINDOW = 0x00000080;
        private const int WS_EX_APPWINDOW = 0x00040000;

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        private IntPtr _gameProcessHandle = IntPtr.Zero;
        private float _nameAnimationProgress = 0f;
        private long _lastUpdateTime = 0;
        private Dictionary<string, Color> playerColors = new Dictionary<string, Color>();
        private Random random = new Random();

        protected override unsafe void Render()
        {
            try
            {
                if (!Core.HaveMatrix) return;

                // Update animation timer
                long currentTime = Environment.TickCount64;
                if (_lastUpdateTime == 0) _lastUpdateTime = currentTime;
                float deltaTime = (currentTime - _lastUpdateTime) / 1000f;
                _lastUpdateTime = currentTime;

                // Smooth animation for name/distance
                _nameAnimationProgress = Math.Clamp(_nameAnimationProgress + (Config.ESPName ? deltaTime * 2f : -deltaTime * 4f), 0f, 1f);

                // Initialize game process
                if (!InitializeGameProcess())
                {
                    Console.WriteLine("Failed to initialize game process.");
                    return;
                }

                CreateHandle();

                string text = "";
            

                var windowWidth = Core.Width;
                var windowHeight = Core.Height;
                var textSize = ImGui.CalcTextSize(text);
                var textPosX = (windowWidth - textSize.X) / 2;
                var textPosY = 80;
                uint textColor = ImGui.ColorConvertFloat4ToU32(new Vector4(1.0f, 1.0f, 1.0f, 1.0f));
                uint shadowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(0.0f, 0.0f, 0.0f, 0.5f));

                var drawList = ImGui.GetForegroundDrawList();

                var offsets = new[] { new Vector2(1, 0), new Vector2(-1, 0), new Vector2(0, 1), new Vector2(0, -1) };
                foreach (var offset in offsets)
                {
                    drawList.AddText(new Vector2(textPosX + offset.X, textPosY + offset.Y), shadowColor, text);
                }

                drawList.AddText(new Vector2(textPosX, textPosY), textColor, text);

                var tmp = Core.Entities;

                string windowName = "Overlay";
                hWnd = FindWindow(null, windowName);
                HDPlayer = FindWindow("BlueStacksApp", null);

                if (hWnd != IntPtr.Zero)
                {
                    int extendedStyle = GetWindowLong(hWnd, GWL_EXSTYLE);
                    SetWindowLong(hWnd, GWL_EXSTYLE, (extendedStyle | WS_EX_TOOLWINDOW) & ~WS_EX_APPWINDOW);
                }
                else
                {
                    Console.WriteLine("La finestra non è stata trovata.");
                }

                foreach (var entity in tmp.Values)
                {
                    if (entity.IsDead || !entity.IsKnown)
                    {
                        continue;
                    }

                    var dist = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                    if (dist > Config.espran) continue;

                    var headScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                    var bottomScreenPos = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);

                    float CornerHeight = Math.Abs(headScreenPos.Y - bottomScreenPos.Y);
                    float CornerWidth = (float)(CornerHeight * 0.65);

                    if (Config.ESPBox)
                    {
                        uint boxColor = ColorToUint32(Config.ESPBoxColor);
                        DrawGlowBox(headScreenPos.X - (CornerWidth / 2), headScreenPos.Y, CornerWidth, CornerHeight, boxColor, 1f, Config.GlowRadius, Config.FeatherAmount, Config.GlowOpacity);
                    }

                    if (Config.ESPFillBox)
                    {
                        uint boxColor = ColorToUint32(Color.FromArgb((int)(0.2f * 255), Config.ESPFillBoxColor));
                        DrawFilledBox(headScreenPos.X - (CornerWidth / 2), headScreenPos.Y, CornerWidth, CornerHeight, boxColor);
                    }

                    // --- ESP LINE ---
                    if (Config.ESPLine)
                    {
                        Vector2 lineStart = new Vector2(Core.Width / 2f, 25f);
                        Vector2 lineEnd = new Vector2(headScreenPos.X, headScreenPos.Y);
                        float increasedGlowRadius = Config.GlowRadius * 2f;

                        uint lineColor = (entity.Health <= 1 || entity.IsKnocked)
                            ? ColorToUint32(Color.Gray)
                            : ColorToUint32(Config.ESPLineColor);

                        DrawGlowLine(
                            lineStart,
                            lineEnd,
                            lineColor,
                            1f,
                            increasedGlowRadius,
                            Config.FeatherAmount,
                            Config.GlowOpacity
                        );
                    }

                    // --- HEALTH BAR ---
                    if (Config.ESPHealth)
                    {
                        Vector2 healthBarPos = new Vector2(headScreenPos.X - (CornerWidth / 2) - 7, headScreenPos.Y);

                        float healthPercentage = entity.Health > 1000 ? 1f :
                                                 entity.Health < 0 ? 0f :
                                                 (float)entity.Health / (entity.Health > 230 ? 500 : 200);

                        float healthBarVerticalHeight = CornerHeight * (1 - healthPercentage);

                        // Colore della barra della salute basato SOLO sulla percentuale
                        Color barColor;
                        if (healthPercentage > 0.8f)
                            barColor = Color.Lime;
                        else if (healthPercentage > 0.6f)
                            barColor = Color.Yellow;
                        else if (healthPercentage > 0.3f)
                            barColor = Color.Orange;
                        else
                            barColor = Color.Red;

                        float glowSize = 1.0f;

                        // Glow dietro la barra
                        ImGui.GetForegroundDrawList().AddRectFilled(
                            new Vector2(healthBarPos.X - glowSize, healthBarPos.Y - glowSize),
                            new Vector2(healthBarPos.X + 2 + glowSize, healthBarPos.Y + CornerHeight + glowSize),
                            ColorToUint32(Color.FromArgb(80, barColor.R, barColor.G, barColor.B))
                        );

                        // Background della barra
                        ImGui.GetForegroundDrawList().AddRectFilled(
                            healthBarPos,
                            new Vector2(healthBarPos.X + 2, healthBarPos.Y + CornerHeight),
                            0x90000000 // Grigio scuro trasparente
                        );

                        // Parte colorata della barra
                        ImGui.GetForegroundDrawList().AddRectFilled(
                            new Vector2(healthBarPos.X, healthBarPos.Y + healthBarVerticalHeight),
                            new Vector2(healthBarPos.X + 2, healthBarPos.Y + CornerHeight),
                            ColorToUint32(barColor)
                        );
                    }

                    // Combined Name and Distance with animation
                    if (Config.ESPName && _nameAnimationProgress > 0.01f)
                    {
                        string entityName = string.IsNullOrEmpty(entity.Name) ? "BOT" : entity.Name;
                        string displayText = $"{entityName} [{MathF.Round(dist)}m]";

                        // Diminuire dimensione base
                        float baseSize = Math.Max(10f, 14f - (dist / 60f));
                        float animatedSize = baseSize * _nameAnimationProgress;

                        Vector2 textSize1 = ImGui.CalcTextSize(displayText);
                        float textX = headScreenPos.X - (textSize1.X / 2);
                        float textY = headScreenPos.Y - 20 * _nameAnimationProgress;

                        ImGui.PushFont(ImGui.GetIO().Fonts.Fonts[0]); // Font predefinito

                        float glowRadius = 10f * _nameAnimationProgress;
                        float feather = 1.5f;
                        float glowOpacityMultiplier = 0.02f * _nameAnimationProgress;

                        // Usa il colore fisso da Config
                        uint playerColorUint = ColorToUint32(Config.ESPNameColor);

                        DrawGlowText(
                            new Vector2(textX, textY),
                            playerColorUint,
                            displayText,
                            glowRadius,
                            feather,
                            glowOpacityMultiplier
                        );

                        ImGui.PopFont();
                    }


                    // Weapon ESP
                    if (Config.ESPWeapon)
                    {
                        DrawWeaponTextESP(entity, headScreenPos, CornerHeight);
                    }

                    // Weapon Icons
                    if (Config.ESPWeaponIcon)
                    {
                        Vector2 fixedNameSize = new Vector2(95, 16);

                        if (headScreenPos.X >= 0 && headScreenPos.Y >= 0 && headScreenPos.X <= Core.Width && headScreenPos.Y <= Core.Height)
                        {
                            Vector2 namePos = new Vector2(headScreenPos.X - fixedNameSize.X / 2, headScreenPos.Y - fixedNameSize.Y - 15);

                            try
                            {
                                IntPtr imagehandle;
                                AddOrGetImagePointer(entity.WeaponIconPath, true, out imagehandle, out var width, out var height);
                                {
                                    Vector2 iconSize = new Vector2(60, 20);
                                    Vector2 iconPos = new Vector2(namePos.X + (fixedNameSize.X - iconSize.X) / 2, namePos.Y - iconSize.Y - 2);
                                    ImGui.GetForegroundDrawList().AddImage(imagehandle, iconPos, iconPos + iconSize);
                                }
                            }
                            catch { }
                        }
                    }

                    // Skeleton ESP
                    if (Config.ESPSkeleton)
                    {
                        uint skeletonColor = ColorToUint32(Config.ESPSkeletonColor);
                        DrawSkeleton(entity, skeletonColor);
                    }

                

                    if (Config.AIMFF)
                    {
                        var center = new Vector2(Core.Width / 2f, Core.Height / 2f);
                        float radius = Math.Min(Config.AimBotFov, Math.Min(Core.Width, Core.Height) / 2f);
                        ImGui.GetBackgroundDrawList().AddCircle(center, radius, ColorToUint32(Config.Aimfovcolor), 100, 1f);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ESP rendering: {ex.Message}");
                // Reset state to recover on next frame
                _gameProcessHandle = IntPtr.Zero;
            }
        }

        private bool InitializeGameProcess()
        {
            try
            {
                if (_gameProcessHandle != IntPtr.Zero)
                    return true;

                var processes = Process.GetProcessesByName("HD-Player");
                if (processes.Length > 0)
                {
                    _gameProcessHandle = OpenProcess(PROCESS_VM_READ, false, processes[0].Id);
                    return _gameProcessHandle != IntPtr.Zero;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error opening process: {ex.Message}");
                _gameProcessHandle = IntPtr.Zero;
            }
            return false;
        }

        public void DrawFilledBox(float X, float Y, float W, float H, uint color)
        {
            var vList = ImGui.GetForegroundDrawList();
            vList.AddRectFilled(new Vector2(X, Y), new Vector2(X + W, Y + H), color);
        }

        public void DrawFilledCircle(float centerY, float radius, int numSegments = 64)
        {
            var vList = ImGui.GetBackgroundDrawList();
            float centerX = Core.Width / 2f;
            uint color = ColorToUint32(Color.FromArgb((int)(1f * 255), 225, 0, 0));
            float shadowOffset = 1.5f;
            uint shadowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(0f, 0f, 0f, 1f));

            vList.AddCircleFilled(new Vector2(centerX, centerY), radius + shadowOffset, shadowColor, numSegments);
            vList.AddCircleFilled(new Vector2(centerX, centerY), radius, color, numSegments);
        }

        public void DrawGlowBox(float X, float Y, float W, float H, uint color, float thickness, float glowRadius, float feather, float glowOpacity)
        {
            var vList = ImGui.GetForegroundDrawList();
            float lineW = W / 4;
            float lineH = H / 4;

            DrawGlowLine(new Vector2(X, Y - thickness / 3), new Vector2(X, Y + lineH), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X - thickness / 3, Y), new Vector2(X + lineW, Y), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X + W - lineW, Y), new Vector2(X + W + thickness / 2, Y), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X + W, Y - thickness / 3), new Vector2(X + W, Y + lineH), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X, Y + H - lineH), new Vector2(X, Y + H + thickness / 2), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X - thickness / 3, Y + H), new Vector2(X + lineW, Y + H), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X + W - lineW, Y + H), new Vector2(X + W + thickness / 2, Y + H), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
            DrawGlowLine(new Vector2(X + W, Y + H - lineH), new Vector2(X + W, Y + H + thickness / 2), color, thickness, glowRadius, feather, glowOpacity * GlowIntensityBox);
        }

        void DrawSkeleton(Entity entity, uint skeletonColor)
        {
            var vList = ImGui.GetForegroundDrawList();

            void DrawBone(Vector3 start, Vector3 end, uint color)
            {
                var startPos = W2S.WorldToScreen(Core.CameraMatrix, start, Core.Width, Core.Height);
                var endPos = W2S.WorldToScreen(Core.CameraMatrix, end, Core.Width, Core.Height);
                if (startPos.X > 1 && startPos.Y > 1 && endPos.X > 1 && endPos.Y > 1)
                {
                    DrawGlowLine(new Vector2(startPos.X, startPos.Y), new Vector2(endPos.X, endPos.Y), color, 1.5f, Config.GlowRadius, Config.FeatherAmount, Config.GlowOpacity);
                }
            }

            DrawBone(entity.Head, entity.Neck, skeletonColor);
            DrawBone(entity.Neck, entity.Pelvis, skeletonColor);
            DrawBone(entity.Neck, entity.ShoulderR, skeletonColor);
            DrawBone(entity.Neck, entity.ShoulderL, skeletonColor);
            DrawBone(entity.ShoulderR, entity.ElbowR, skeletonColor);
            DrawBone(entity.ElbowR, entity.HandR, skeletonColor);
            DrawBone(entity.ShoulderL, entity.ElbowL, skeletonColor);
            DrawBone(entity.ElbowL, entity.HandL, skeletonColor);
            DrawBone(entity.Pelvis, entity.FootR, skeletonColor);
            DrawBone(entity.Pelvis, entity.FootL, skeletonColor);
            DrawBone(entity.FootR, entity.KneeR, skeletonColor);
            DrawBone(entity.FootL, entity.KneeL, skeletonColor);
        }

        public void DrawGlowText(Vector2 pos, uint color, string text, float glowRadius, float feather, float glowOpacityMultiplier)
        {
            var drawList = ImGui.GetForegroundDrawList();
            Vector4 colorVec = ImGui.ColorConvertU32ToFloat4(color);

            for (float i = glowRadius; i > 0; i -= feather)
            {
                float alpha = colorVec.W * (i / glowRadius) * glowOpacityMultiplier * GlowIntensityName;
                alpha = Clamp(alpha, 0, 1);

                uint glowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(colorVec.X, colorVec.Y, colorVec.Z, alpha));

                drawList.AddText(ImGui.GetFont(), ImGui.GetFontSize(), new Vector2(pos.X + i, pos.Y), glowColor, text);
            }

            drawList.AddText(ImGui.GetFont(), ImGui.GetFontSize(), pos, color, text);
        }

        public void DrawGlowLine(Vector2 start, Vector2 end, uint color, float thickness, float glowRadius, float feather, float glowOpacityMultiplier)
        {
            var drawList = ImGui.GetBackgroundDrawList();
            Vector4 colorVec = ImGui.ColorConvertU32ToFloat4(color);

            for (float i = glowRadius; i > 0; i -= feather)
            {
                float alpha = colorVec.W * (i / glowRadius) * glowOpacityMultiplier * GlowIntensitySkeleton;
                alpha = Clamp(alpha, 0, 1);

                uint glowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(colorVec.X, colorVec.Y, colorVec.Z, alpha));

                drawList.AddLine(start, end, glowColor, thickness + (glowRadius - i) * 0.5f);
            }

            drawList.AddLine(start, end, color, thickness);

            for (float i = glowRadius; i > 0; i -= feather)
            {
                float alpha = colorVec.W * (i / glowRadius) * glowOpacityMultiplier * GlowIntensitySkeleton;
                alpha = Clamp(alpha, 0, 1);

                uint glowColor = ImGui.ColorConvertFloat4ToU32(new Vector4(colorVec.X, colorVec.Y, colorVec.Z, alpha));

                float radius = thickness / 2 + (glowRadius - i) * 0.5f;
                drawList.AddCircleFilled(start, radius, glowColor);
                drawList.AddCircleFilled(end, radius, glowColor);
            }

            drawList.AddCircleFilled(start, thickness / 2, color);
            drawList.AddCircleFilled(end, thickness / 2, color);
        }

        private float Clamp(float value, float min, float max)
        {
            return (value < min) ? min : (value > max) ? max : value;
        }

        private Color GetPlayerColor(string playerName)
        {
            if (!playerColors.ContainsKey(playerName))
            {
                playerColors[playerName] = Color.FromArgb(random.Next(256), random.Next(256), random.Next(256));
            }
            return playerColors[playerName];
        }

        static uint ColorToUint32(Color color)
        {
            return ImGui.ColorConvertFloat4ToU32(new Vector4(
                (float)(color.R / 255.0),
                (float)(color.G / 255.0),
                (float)(color.B / 255.0),
                (float)(color.A / 255.0)));
        }

        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetWindowDisplayAffinity(IntPtr hWnd, uint dwAffinity);

        const uint WDA_NONE = 0x00000000;
        const uint WDA_MONITOR = 0x00000001;
        const uint WDA_EXCLUDEFROMCAPTURE = 0x00000011;

        void CreateHandle()
        {
            RECT rect;
            GetWindowRect(Core.Handle, out rect);
            int x = rect.Left;
            int y = rect.Top;
            int width = rect.Right - rect.Left;
            int height = rect.Bottom - rect.Top;
            ImGui.SetWindowSize(new Vector2((float)width, (float)height));
            ImGui.SetWindowPos(new Vector2((float)x, (float)y));
            Size = new Size(width, height);
            Position = new Point(x, y);

            Core.Width = width;
            Core.Height = height;
            if (Config.StreamMode)
            {
                SetWindowDisplayAffinity(hWnd, WDA_EXCLUDEFROMCAPTURE);
            }
            else
            {
                SetWindowDisplayAffinity(hWnd, WDA_NONE);
            }
        }

        void DrawWeaponTextESP(Entity entity, Vector2 headScreenPos, float boxHeight)
        {
            if (!Config.ESPWeapon || entity == null || string.IsNullOrEmpty(entity.WeaponName))
                return;

            Vector2 weaponTextPos = new Vector2(headScreenPos.X - ImGui.CalcTextSize(entity.WeaponName).X / 2, headScreenPos.Y + boxHeight + 5);

            uint outlineColor = ColorToUint32(Color.Black);
            uint textColor = ColorToUint32(Color.White);
            float outlineOffset = 1f;

            Vector2[] offsets = new Vector2[] {
                new Vector2(-outlineOffset, -outlineOffset),
                new Vector2(outlineOffset, -outlineOffset),
                new Vector2(-outlineOffset, outlineOffset),
                new Vector2(outlineOffset, outlineOffset)
            };

            foreach (var offset in offsets)
            {
                ImGui.GetForegroundDrawList().AddText(weaponTextPos + offset, outlineColor, entity.WeaponName);
            }

            ImGui.GetForegroundDrawList().AddText(weaponTextPos, textColor, entity.WeaponName);
        }
    }
}
