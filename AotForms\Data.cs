﻿using Client;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Numerics;
using System.Runtime.InteropServices;
using System.Text;

namespace AotForms
{
    internal static class Data
    {
        [StructLayout(LayoutKind.Sequential)]
        private struct WeaponInfo
        {
            public IntPtr VTable;
            public int WeaponShowID;
            public IntPtr WeaponName;
        }

        internal static void Work()
        {
            while (true)
            {
                Core.HaveMatrix = false;
                Matrix4x4 viewMatrix = Matrix4x4.Identity;

                if (!InitializeGameStructures(out uint currentGame, out uint localPlayer, out Vector3 mainPos, ref viewMatrix))
                {
                    ResetCache();
                    continue;
                }

                ProcessAllEntities(currentGame, localPlayer, mainPos);
            }
        }

        private static bool InitializeGameStructures(out uint currentGame, out uint localPlayer, out Vector3 mainPos, ref Matrix4x4 viewMatrix)
        {
            // Initialize all out parameters
            currentGame = 0;
            localPlayer = 0;
            mainPos = Vector3.Zero;

            // Base game facade
            if (!InternalMemory.Read<uint>(Offsets.Il2Cpp + Offsets.InitBase, out var baseGameFacade) || baseGameFacade == 0)
                return false;

            // Game facade
            if (!InternalMemory.Read<uint>(baseGameFacade, out var gameFacade) || gameFacade == 0)
                return false;

            // Static game facade
            if (!InternalMemory.Read<uint>(gameFacade + Offsets.StaticClass, out var staticGameFacade) || staticGameFacade == 0)
                return false;

            // Current game
            if (!InternalMemory.Read<uint>(staticGameFacade, out currentGame) || currentGame == 0)
                return false;

            // Current match
            if (!InternalMemory.Read<uint>(currentGame + Offsets.CurrentMatch, out var currentMatch) || currentMatch == 0)
                return false;

            // Local player
            if (!InternalMemory.Read<uint>(currentMatch + Offsets.LocalPlayer, out localPlayer) || localPlayer == 0)
                return false;

            Core.LocalPlayer = localPlayer;

            // Main camera transform
            if (!InternalMemory.Read<uint>(localPlayer + Offsets.MainCameraTransform, out var mainTransform) || mainTransform == 0)
                return false;

            if (!Transform.GetPosition(mainTransform, out mainPos))
                return false;

            Core.LocalMainCamera = mainPos;

            // Follow camera
            if (!InternalMemory.Read<uint>(localPlayer + Offsets.FollowCamera, out var followCamera) || followCamera == 0)
                return false;

            // Camera
            if (!InternalMemory.Read<uint>(followCamera + Offsets.Camera, out var camera) || camera == 0)
                return false;

            // Camera base
            if (!InternalMemory.Read<uint>(camera + 0x8, out var cameraBase) || cameraBase == 0)
                return false;

            // View matrix
            if (!InternalMemory.Read<Matrix4x4>(cameraBase + Offsets.ViewMatrix, out viewMatrix))
                return false;

            Core.HaveMatrix = true;
            Core.CameraMatrix = viewMatrix;
            return true;
        }

        private static void ProcessAllEntities(uint currentGame, uint localPlayer, Vector3 mainPos)
        {
            if (!InternalMemory.Read<uint>(currentGame + Offsets.DictionaryEntities, out var entityDictionary) || entityDictionary == 0)
                return;

            if (!InternalMemory.Read<uint>(entityDictionary + 0x14, out var entities) || entities == 0)
                return;

            entities += 0x10;

            if (!InternalMemory.Read<uint>(entityDictionary + 0x18, out var entitiesCount) || entitiesCount < 1)
                return;

            for (int i = 0; i < (int)entitiesCount; i++)
            {
                ProcessSingleEntity(i, entities, localPlayer, mainPos);
            }
        }

        private static void ProcessSingleEntity(int index, uint entities, uint localPlayer, Vector3 mainPos)
        {
            if (!InternalMemory.Read<uint>((ulong)(index * 0x4 + entities), out var entityAddress) || entityAddress == 0)
                return;

            if (entityAddress == localPlayer)
                return;

            if (!Core.Entities.TryGetValue(entityAddress, out var currentEntity))
            {
                currentEntity = InitializeNewEntity(entityAddress);
                Core.Entities.TryAdd(entityAddress, currentEntity);
                return;
            }

            UpdateEntityData(currentEntity, entityAddress, mainPos);
        }

        private static Entity InitializeNewEntity(uint entityAddress)
        {
            return new Entity
            {
                BaseAddress = (IntPtr)(long)entityAddress,
                IsTeam = Bool3.Unknown,
                IsKnown = false,
                IsDead = true,
                IsKnocked = false,
                Health = 0,
                Head = Vector3.Zero,
                Root = Vector3.Zero,
                Neck = Vector3.Zero,
                Pelvis = Vector3.Zero,
                ShoulderR = Vector3.Zero,
                ShoulderL = Vector3.Zero,
                ElbowR = Vector3.Zero,
                ElbowL = Vector3.Zero,
                HandR = Vector3.Zero,
                HandL = Vector3.Zero,
                FootR = Vector3.Zero,
                FootL = Vector3.Zero,
                KneeL = Vector3.Zero,
                KneeR = Vector3.Zero,
                Name = "",
                WeaponName = "Unknown",
                Distance = 0
            };
        }

        private static void UpdateEntityData(Entity entity, uint entityAddress, Vector3 mainPos)
        {
            UpdateTeamStatus(entity, entityAddress);
            if (!entity.IsKnown || entity.IsTeam == Bool3.True)
                return;

            UpdatePlayerStatus(entity, entityAddress);
            UpdatePlayerInfo(entity, entityAddress);
            UpdateWeaponData(entity, entityAddress);
            UpdateBonePositions(entity, entityAddress, mainPos);
        }

        private static void UpdateTeamStatus(Entity entity, uint entityAddress)
        {
            if (entity.IsTeam != Bool3.Unknown)
                return;

            if (!InternalMemory.Read<uint>(entityAddress + Offsets.AvatarManager, out var avatarManager) || avatarManager == 0)
                return;

            if (!InternalMemory.Read<uint>(avatarManager + Offsets.Avatar, out var avatar) || avatar == 0)
                return;

            if (!InternalMemory.Read<bool>(avatar + Offsets.Avatar_IsVisible, out var isVisible) || !isVisible)
                return;

            if (!InternalMemory.Read<uint>(avatar + Offsets.Avatar_Data, out var avatarData) || avatarData == 0)
                return;

            if (!InternalMemory.Read<bool>(avatarData + Offsets.Avatar_Data_IsTeam, out var isTeam))
                return;

            entity.IsTeam = isTeam ? Bool3.True : Bool3.False;
            entity.IsKnown = !isTeam;
        }

        private static void UpdatePlayerStatus(Entity entity, uint entityAddress)
        {
            if (Config.IgnoreKnocked)
            {
                if (InternalMemory.Read<uint>(entityAddress + Offsets.Player_ShadowBase, out var shadowBase) && shadowBase != 0)
                {
                    if (InternalMemory.Read<int>(shadowBase + Offsets.XPose, out var xpose))
                    {
                        entity.IsKnocked = xpose == 8;
                    }
                }
            }

            if (InternalMemory.Read<bool>(entityAddress + Offsets.Player_IsDead, out var isDead))
            {
                entity.IsDead = isDead;
            }
        }

        private static void UpdatePlayerInfo(Entity entity, uint entityAddress)
        {
            if (Config.ESPName)
            {
                if (InternalMemory.Read<uint>(entityAddress + Offsets.Player_Name, out var nameAddr) && nameAddr != 0)
                {
                    if (InternalMemory.Read<int>(nameAddr + 0x8, out var nameLen) && nameLen > 0)
                    {
                        var name = InternalMemory.ReadString(nameAddr + 0xC, nameLen);
                        if (!string.IsNullOrEmpty(name))
                        {
                            entity.Name = name;
                        }
                    }
                }
            }

            if (Config.ESPHealth)
            {
                if (InternalMemory.Read<uint>(entityAddress + Offsets.Player_Data, out var dataPool) && dataPool != 0)
                {
                    if (InternalMemory.Read<uint>(dataPool + 0x8, out var poolObj) && poolObj != 0)
                    {
                        if (InternalMemory.Read<uint>(poolObj + 0x10, out var pool) && pool != 0)
                        {
                            if (InternalMemory.Read<short>(pool + 0x10, out var health))
                            {
                                entity.Health = health;
                            }
                        }
                    }
                }
            }
        }

        private static void UpdateWeaponData(Entity entity, uint entityAddress)
        {
            if (Config.ESPWeapon || Config.ESPWeaponIcon)
            {
                var rDataPool = InternalMemory.Read<uint>(entityAddress + Offsets.Player_Data, out var dataPool);
                if (rDataPool && dataPool != 0)
                {
                    var rPoolObj = InternalMemory.Read<uint>(dataPool + 0x8, out var poolObj);
                    if (rPoolObj && poolObj != 0)
                    {
                        var rPool = InternalMemory.Read<uint>(poolObj + 0x20, out var pool);
                        if (rPool && pool != 0)
                        {
                            var rweaponid = InternalMemory.Read<short>(pool + 0x10, out var weaponid);
                            if (rweaponid && weaponid != 0)
                            {
                                var weaponInfo = WeaponIndex.GetWeaponInfo(weaponid);
                                entity.WeaponName = weaponInfo.Name;

                                // Se vuoi anche l'icona:
                                if (Config.ESPWeaponIcon)
                                {
                                    entity.WeaponIconPath = weaponInfo.IconPath; // Aggiungi questa proprietà a Entity se ti serve
                                }
                            }
                        }
                    }
                }
            }
        }

        private static void LogToForm(string message)
        {
            try
            {
                Form5.Instance?.UpdateWeaponLog(message);
            }
            catch { /* Ignora errori UI */ }
        }

        private static void UpdateBonePositions(Entity entity, uint entityAddress, Vector3 mainPos)
        {
            UpdateBonePosition(entityAddress, Bones.Head, pos => {
                entity.Head = pos;
                entity.Distance = Vector3.Distance(mainPos, pos);
            });

            UpdateBonePosition(entityAddress, Bones.Root, pos => entity.Root = pos);
            UpdateBonePosition(entityAddress, Bones.Neck, pos => entity.Neck = pos);
            UpdateBonePosition(entityAddress, Bones.Pelvis, pos => entity.Pelvis = pos);
            UpdateBonePosition(entityAddress, Bones.ShoulderR, pos => entity.ShoulderR = pos);
            UpdateBonePosition(entityAddress, Bones.ShoulderL, pos => entity.ShoulderL = pos);
            UpdateBonePosition(entityAddress, Bones.ElbowR, pos => entity.ElbowR = pos);
            UpdateBonePosition(entityAddress, Bones.ElbowL, pos => entity.ElbowL = pos);
            UpdateBonePosition(entityAddress, Bones.HandR, pos => entity.HandR = pos);
            UpdateBonePosition(entityAddress, Bones.HandL, pos => entity.HandL = pos);
            UpdateBonePosition(entityAddress, Bones.FootR, pos => entity.FootR = pos);
            UpdateBonePosition(entityAddress, Bones.FootL, pos => entity.FootL = pos);
            UpdateBonePosition(entityAddress, Bones.KneeL, pos => entity.KneeL = pos);
            UpdateBonePosition(entityAddress, Bones.KneeR, pos => entity.KneeR = pos);
        }

        private static void UpdateBonePosition(uint entityAddress, Bones bone, Action<Vector3> setPosition)
        {
            if (InternalMemory.Read<uint>(entityAddress + (uint)bone, out var boneAddr) && boneAddr != 0)
            {
                if (Transform.GetNodePosition(boneAddr, out var position))
                {
                    setPosition(position);
                }
            }
        }

        private static void ResetCache()
        {
            Core.Entities = new ConcurrentDictionary<ulong, Entity>();
            InternalMemory.Cache = new Dictionary<ulong, ulong>();
        }
    }
}