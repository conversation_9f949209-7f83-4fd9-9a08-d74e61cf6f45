﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net7.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <PublishAot>true</PublishAot>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <_SuppressWinFormsTrimError>true</_SuppressWinFormsTrimError>
    <Platforms>AnyCPU;x64</Platforms>
    <AssemblyName>shg</AssemblyName>
  </PropertyGroup>
  <ItemGroup>
    <RdXmlFile Include="rd.xml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="ClickableTransparentOverlay" Version="9.3.0" />
    <PackageReference Include="Costura.Fody" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="DNNE" Version="2.0.6" />
    <PackageReference Include="Guna.UI2.WinForms" Version="2.0.4.6" />
    <PackageReference Include="MouseKeyHook" Version="5.7.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="WinFormsComInterop" Version="0.5.0" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Remove="clicksound.wav" />
    <None Remove="Properties\n8ow9e.dll" />
    <None Remove="Properties\WeaponIcons\ac80.png" />
    <None Remove="Properties\WeaponIcons\ak47.png" />
    <None Remove="Properties\WeaponIcons\an94.png" />
    <None Remove="Properties\WeaponIcons\aug.png" />
    <None Remove="Properties\WeaponIcons\AWM-Y.png" />
    <None Remove="Properties\WeaponIcons\awm.png" />
    <None Remove="Properties\WeaponIcons\bizon.png" />
    <None Remove="Properties\WeaponIcons\chargebuster.png" />
    <None Remove="Properties\WeaponIcons\desert-eagle.png" />
    <None Remove="Properties\WeaponIcons\famas.png" />
    <None Remove="Properties\WeaponIcons\fist.png" />
    <None Remove="Properties\WeaponIcons\g18.png" />
    <None Remove="Properties\WeaponIcons\g36.png" />
    <None Remove="Properties\WeaponIcons\GLOO WALL.png" />
    <None Remove="Properties\WeaponIcons\groza.png" />
    <None Remove="Properties\WeaponIcons\heal-pistol.png" />
    <None Remove="Properties\WeaponIcons\healsniper.png" />
    <None Remove="Properties\WeaponIcons\kar98k.png" />
    <None Remove="Properties\WeaponIcons\kingfisher.png" />
    <None Remove="Properties\WeaponIcons\kord.png" />
    <None Remove="Properties\WeaponIcons\m1014.png" />
    <None Remove="Properties\WeaponIcons\m14.png" />
    <None Remove="Properties\WeaponIcons\m1873.png" />
    <None Remove="Properties\WeaponIcons\m1887.png" />
    <None Remove="Properties\WeaponIcons\m1917.png" />
    <None Remove="Properties\WeaponIcons\m24.png" />
    <None Remove="Properties\WeaponIcons\m249.png" />
    <None Remove="Properties\WeaponIcons\m4a1.png" />
    <None Remove="Properties\WeaponIcons\m500.png" />
    <None Remove="Properties\WeaponIcons\m590.png" />
    <None Remove="Properties\WeaponIcons\m60.png" />
    <None Remove="Properties\WeaponIcons\m82b.png" />
    <None Remove="Properties\WeaponIcons\mac10.png" />
    <None Remove="Properties\WeaponIcons\mag7.png" />
    <None Remove="Properties\WeaponIcons\mini-uzi.png" />
    <None Remove="Properties\WeaponIcons\mp40.png" />
    <None Remove="Properties\WeaponIcons\mp5.png" />
    <None Remove="Properties\WeaponIcons\p90.png" />
    <None Remove="Properties\WeaponIcons\parafal.png" />
    <None Remove="Properties\WeaponIcons\plasma.png" />
    <None Remove="Properties\WeaponIcons\scar.png" />
    <None Remove="Properties\WeaponIcons\shield-gun.png" />
    <None Remove="Properties\WeaponIcons\sks.png" />
    <None Remove="Properties\WeaponIcons\spas12.png" />
    <None Remove="Properties\WeaponIcons\svd.png" />
    <None Remove="Properties\WeaponIcons\thompson.png" />
    <None Remove="Properties\WeaponIcons\trogon.png" />
    <None Remove="Properties\WeaponIcons\ump.png" />
    <None Remove="Properties\WeaponIcons\usp.png" />
    <None Remove="Properties\WeaponIcons\usp2.png" />
    <None Remove="Properties\WeaponIcons\vector.png" />
    <None Remove="Properties\WeaponIcons\vsk94.png" />
    <None Remove="Properties\WeaponIcons\vss.png" />
    <None Remove="Properties\WeaponIcons\woodpecker.png" />
    <None Remove="Properties\WeaponIcons\xm8.png" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\WeaponIcons\ac80.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\ak47.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\an94.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\aug.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\awm.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\AWM-Y.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\bizon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\chargebuster.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\desert-eagle.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\famas.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\fist.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\g18.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\g36.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\GLOO WALL.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\groza.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\heal-pistol.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\healsniper.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\kar98k.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\kingfisher.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\kord.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m1014.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m14.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m1873.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m1887.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m1917.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m24.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m249.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m4a1.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m500.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m590.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m60.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\m82b.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\mac10.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\mag7.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\mini-uzi.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\mp40.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\mp5.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\p90.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\parafal.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\plasma.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\scar.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\shield-gun.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\sks.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\spas12.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\svd.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\thompson.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\trogon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\ump.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\usp.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\usp2.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\vector.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\vsk94.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\vss.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\woodpecker.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\WeaponIcons\xm8.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="clicksound.wav" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Resources\Applause.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Arm Wave.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Battle Dance.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Big Smash.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Chicken.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Dab.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Devils Move.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Dragon Fist.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Evo - Ak.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Evo - Famas.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Evo - M1014.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Evo - Mp40.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Evo - Scar.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Evo - Xm8.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\FFWC Throne.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Furious Slam.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Hadouken.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Hello.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\High Five.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\I Heat You.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Jaguar Dance.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Lets Go.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\LOL Emote.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Mummy Dance.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Party Dance.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Provoke.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Push-up.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Shimmy.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Shoot Dance.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Resources\Threaten.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>