﻿using System.Media;
using System.Numerics;
using System.Reflection;
using System.Windows.Forms; // Assicurati di includere questo namespace

namespace AotForms
{
    internal static class Config
    {
        internal static bool flyme = false;
        internal static bool UpPlayer = false;
        internal static bool AimBot = false;
        internal static bool AimBotLeft = false;
        internal static float aimlegit = 0.05f;
        internal static int Aimbotype = 0;
        internal static bool AIKILL = false;
        internal static bool AIMFF = false;
       
        public static int AimbotKey = (int)Keys.RButton;

        public static bool TriggerBot = true;
        public static int TriggerBotKey = (int)Keys.LShiftKey; // زر لتفعيل triggerbot مؤقتًا

        public static float AimBotMaxDistance = 150f;
    
        public static float Smoothing = 0.1f;


        

    
        internal static int expsize = 8;
        internal static int Aimfov = 100;
        internal static int espran = 150;
        internal static int aimdelay = 0;
        public static float GlowRadius = 15;
        public static float FeatherAmount = 2f;
        public static float GlowOpacity = 0.02f;
        internal static bool EspBottom = false;
        internal static bool EspUp = false;
        internal static bool IgnoreKnocked = false;
        internal static bool Speed = false;
        internal static bool NoRecoil = false;
        internal static bool MagicBullet = false;
        internal static bool NoCache = false;
        internal static bool aimIsVisible = true;
        internal static bool StreamMode = false;
        internal static bool esptotalplyer = false;
        internal static bool FixEsp = false;
        internal static int DelayAim = 0;
        internal static int AimBotFov = 30;
        public static string AimTargetPart { get; set; } = "HEAD";
        internal static bool ESPLine = false;
        public static Color ESPLineColor { get; set; } = Color.Lime; // Colore predefinito della box

        internal static bool ESPBox = false; // Abilita o disabilita l'ESP box

        public static Color ESPBoxColor { get; set; } = Color.Lime; // Colore predefinito della box
       

        // Abilita o disabilita l'ESP box

        internal static bool ESPBox2 = false;
        internal static Color ESPFillBoxColor = Color.Gray;
        internal static bool ESPName = false;
        internal static Color ESPNameColor = Color.Gray;
        internal static Color ESPWeaponColor = Color.Gray;
        internal static bool ESPHealth = false;
        internal static Color ESPHeath = Color.Gray;

        internal static bool ESPSkeleton = false;
        internal static Color ESPSkeletonColor = Color.DarkGray;

        internal static bool ESPFillBox = false;
        internal static Keys ShootKey = Keys.LButton; // Definisci ShootKey come un valore di tipo Keys

        internal static bool ESPCorner = false;
        internal static bool ESPCornerColor = false;

        internal static bool ESPInfo = false;
        internal static bool AimbotSilent = false;
        internal static bool proxtelekill = false;
        internal static bool ESPFove = false;
        internal static bool espbg = false;
        internal static bool Aimfovc = false;
        internal static Color Aimfovcolor = Color.White;
       
        internal static bool ESPWeapon = false; // Abilita/disabilita la visualizzazione dell'arma

        internal static bool espcfx = false;
        internal static bool sound = false;
        internal static bool ESPWeaponIcon = false;
    
        internal static string linePosition = "Up";// Memorizza il nome dell'arma del giocatore

        // Altre proprietà e metodi della classe Config
        public static void Notif()
        {
            if (!sound)
            {
                // Replace "YourNamespace.YourMP3File.mp3" with the correct namespace and file name
                Stream stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("Client.clicksound.wav");

                if (stream != null)
                {
                    using (SoundPlayer player = new SoundPlayer(stream))
                    {
                        player.Play();
                    }
                }
                else
                {
                    // Gestisci il caso in cui il file non viene trovato
                }
            }
            else
            {
                // Gestisci il caso in cui il suono è disabilitato
            }
        }
    }
}
