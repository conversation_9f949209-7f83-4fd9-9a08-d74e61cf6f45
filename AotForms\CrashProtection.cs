using System;
using System.Collections.Concurrent;
using System.IO;
using System.Runtime.ExceptionServices;
using System.Threading;
using System.Threading.Tasks;

namespace AotForms
{
    /// <summary>
    /// Advanced crash protection and recovery system
    /// </summary>
    internal static class CrashProtection
    {
        private static readonly ConcurrentDictionary<string, CrashInfo> _crashHistory = new();
        private static System.Threading.Timer _healthCheckTimer;
        private static volatile bool _isProtectionActive = false;
        private static readonly object _lockObject = new object();

        public class CrashInfo
        {
            public DateTime Timestamp { get; set; }
            public string ErrorMessage { get; set; }
            public string StackTrace { get; set; }
            public int OccurrenceCount { get; set; }
            public bool IsRecoverable { get; set; }
        }

        /// <summary>
        /// Initialize crash protection system
        /// </summary>
        //public static void Initialize()
        //{
        //    lock (_lockObject)
        //    {
        //        if (_isProtectionActive) return;

        //        try
        //        {
        //            // Set up global exception handlers
        //            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        //            AppDomain.CurrentDomain.FirstChanceException += OnFirstChanceException;
        //            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

        //            // Start health monitoring
        //            _healthCheckTimer = new System.Threading.Timer(PerformHealthCheck, null,
        //                TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

        //            _isProtectionActive = true;
        //            ErrorHandler.LogInfo("Crash protection system initialized");
        //        }
        //        catch (Exception ex)
        //        {
        //            ErrorHandler.LogError("Failed to initialize crash protection", ex);
        //        }
        //    }
        //}

        /// <summary>
        /// Handle unhandled exceptions
        ///// </summary>
        //private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        //{
        //    try
        //    {
        //        if (e.ExceptionObject is Exception ex)
        //        {
        //            var crashInfo = RecordCrash(ex, "UnhandledException");

        //            ErrorHandler.LogError($"CRITICAL: Unhandled exception (Terminating: {e.IsTerminating})", ex);

        //            if (!e.IsTerminating && crashInfo.IsRecoverable)
        //            {
        //                AttemptRecovery("UnhandledException", ex);
        //            }
        //            else
        //            {
        //                PerformEmergencyShutdown(ex);
        //            }
        //        }
        //    }
        //    catch (Exception logEx)
        //    {
        //        // Last resort - write to file directly
        //        try
        //        {
        //            File.AppendAllText("emergency_crash.log",
        //                $"[{DateTime.Now}] CRITICAL ERROR in crash handler: {logEx.Message}\n");
        //        }
        //        catch
        //        {
        //            // Nothing more we can do
        //        }
        //    }
        //}

        /// <summary>
        /// Handle first chance exceptions for early detection
        /// </summary>
        private static void OnFirstChanceException(object sender, FirstChanceExceptionEventArgs e)
        {
            try
            {
                // Only track specific exception types that might indicate problems
                if (e.Exception is OutOfMemoryException ||
                    e.Exception is StackOverflowException ||
                    e.Exception is AccessViolationException ||
                    e.Exception is InvalidOperationException)
                {
                    RecordCrash(e.Exception, "FirstChance");
                }
            }
            catch
            {
                // Ignore errors in first chance handler to avoid recursion
            }
        }

        /// <summary>
        /// Handle unobserved task exceptions
        /// </summary>
        //private static void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        //{
        //    try
        //    {
        //        foreach (var ex in e.Exception.InnerExceptions)
        //        {
        //            var crashInfo = RecordCrash(ex, "UnobservedTask");
        //            ErrorHandler.LogError("Unobserved task exception", ex);

        //            if (crashInfo.IsRecoverable)
        //            {
        //                e.SetObserved(); // Mark as observed to prevent termination
        //                AttemptRecovery("UnobservedTask", ex);
        //            }
        //        }
        //    }
        //    catch (Exception logEx)
        //    {
        //        ErrorHandler.LogError("Error in unobserved task exception handler", logEx);
        //    }
        //}

        /// <summary>
        /// Record crash information for analysis
        /// </summary>
        private static CrashInfo RecordCrash(Exception ex, string source)
        {
            try
            {
                var key = $"{source}_{ex.GetType().Name}";
                var crashInfo = _crashHistory.GetOrAdd(key, _ => new CrashInfo
                {
                    Timestamp = DateTime.Now,
                    ErrorMessage = ex.Message,
                    StackTrace = ex.StackTrace,
                    OccurrenceCount = 0,
                    IsRecoverable = DetermineRecoverability(ex)
                });

                crashInfo.OccurrenceCount++;
                crashInfo.Timestamp = DateTime.Now;

                // If same error occurs too frequently, mark as non-recoverable
                if (crashInfo.OccurrenceCount > 5)
                {
                    crashInfo.IsRecoverable = false;
                    ErrorHandler.LogWarning($"Error {key} occurred {crashInfo.OccurrenceCount} times - marking as non-recoverable");
                }

                return crashInfo;
            }
            catch
            {
                // Return default crash info if recording fails
                return new CrashInfo
                {
                    Timestamp = DateTime.Now,
                    ErrorMessage = ex?.Message ?? "Unknown error",
                    StackTrace = ex?.StackTrace ?? "No stack trace",
                    OccurrenceCount = 1,
                    IsRecoverable = false
                };
            }
        }

        /// <summary>
        /// Determine if an exception is recoverable
        /// </summary>
        private static bool DetermineRecoverability(Exception ex)
        {
            // Non-recoverable exceptions
            if (ex is OutOfMemoryException ||
                ex is StackOverflowException ||
                ex is AccessViolationException ||
                ex is AppDomainUnloadedException)
            {
                return false;
            }

            // Recoverable exceptions
            if (ex is ArgumentException ||
                ex is InvalidOperationException ||
                ex is NullReferenceException ||
                ex is IndexOutOfRangeException)
            {
                return true;
            }

            // Default to recoverable for unknown exceptions
            return true;
        }

        /// <summary>
        /// Attempt to recover from an error
        /// </summary>
        //private static void AttemptRecovery(string source, Exception ex)
        //{
        //    try
        //    {
        //        ErrorHandler.LogInfo($"Attempting recovery from {source}: {ex.GetType().Name}");

        //        // General recovery strategies
        //        switch (source)
        //        {
        //            case "UnhandledException":
        //            case "UnobservedTask":
        //                // Clear caches and reset state
        //                InternalMemory.ClearCache();
        //                Core.Entities?.Clear();

        //                // Force garbage collection
        //                GC.Collect(2, GCCollectionMode.Forced);
        //                GC.WaitForPendingFinalizers();

        //                // Reset performance settings
        //                PerformanceConfig.ResetToDefaults();
        //                break;

        //            case "FirstChance":
        //                // Preemptive cleanup for first chance exceptions
        //                if (MemoryPressureMonitor.IsMemoryPressureHigh())
        //                {
        //                    _ = Task.Run(() =>
        //                    {
        //                        InternalMemory.ClearCache();
        //                        GC.Collect(1, GCCollectionMode.Optimized);
        //                    });
        //                }
        //                break;
        //        }

        //        ErrorHandler.LogInfo($"Recovery attempt completed for {source}");
        //    }
        //    catch (Exception recoveryEx)
        //    {
        //        ErrorHandler.LogError($"Recovery failed for {source}", recoveryEx);
        //    }
        //}

        /// <summary>
        /// Perform emergency shutdown
        /// </summary>
        //private static void PerformEmergencyShutdown(Exception ex)
        //{
        //    try
        //    {
        //        ErrorHandler.LogError("EMERGENCY SHUTDOWN INITIATED", ex);

        //        // Save crash dump
        //        SaveCrashDump(ex);

        //        // Stop all tasks
               
        //        RealTimeMonitor.StopMonitoring();
        //        AutoTuner.StopAutoTuning();

        //        // Clear all resources
        //        InternalMemory.ClearCache();
        //        Core.Entities?.Clear();

        //        ErrorHandler.LogInfo("Emergency shutdown completed");
        //    }
        //    catch (Exception shutdownEx)
        //    {
        //        // Write to emergency log
        //        try
        //        {
        //            File.AppendAllText("emergency_shutdown.log",
        //                $"[{DateTime.Now}] Emergency shutdown error: {shutdownEx.Message}\n");
        //        }
        //        catch
        //        {
        //            // Nothing more we can do
        //        }
        //    }
        //}

        /// <summary>
        /// Save crash dump for analysis
        /// </summary>
        //private static void SaveCrashDump(Exception ex)
        //{
        //    try
        //    {
        //        var crashDump = $"CRASH DUMP - {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
        //                       $"Exception: {ex.GetType().Name}\n" +
        //                       $"Message: {ex.Message}\n" +
        //                       $"Stack Trace:\n{ex.StackTrace}\n" +
        //                       $"Memory Usage: {GC.GetTotalMemory(false) / 1024 / 1024}MB\n" +
        //                       $"Cache Size: {InternalMemory.GetCacheSize()}\n" +
        //                       $"Entity Count: {Core.Entities?.Count ?? 0}\n" +
        //                       $"Performance Summary:\n{RealTimeMonitor.GetPerformanceSummary()}\n" +
        //                       $"Configuration:\n{PerformanceConfig.GetConfigurationSummary()}\n";

        //        File.WriteAllText($"crash_dump_{DateTime.Now:yyyyMMdd_HHmmss}.log", crashDump);
        //        ErrorHandler.LogInfo("Crash dump saved");
        //    }
        //    catch (Exception dumpEx)
        //    {
        //        ErrorHandler.LogError("Failed to save crash dump", dumpEx);
        //    }
        //}

        /// <summary>
        /// Perform periodic health checks
        /// </summary>
        //private static void PerformHealthCheck(object state)
        //{
        //    try
        //    {
        //        // Check memory usage
        //        var memoryMB = GC.GetTotalMemory(false) / 1024 / 1024;
        //        if (memoryMB > PerformanceConfig.MemoryPressureThresholdMB * 2) // Double threshold
        //        {
        //            ErrorHandler.LogWarning($"Critical memory usage: {memoryMB}MB");
        //            AttemptRecovery("HealthCheck", new OutOfMemoryException("High memory usage detected"));
        //        }

        //        // Check for excessive errors
        //        var recentErrors = 0;
        //        var cutoffTime = DateTime.Now.AddMinutes(-5);

        //        foreach (var crash in _crashHistory.Values)
        //        {
        //            if (crash.Timestamp > cutoffTime)
        //            {
        //                recentErrors += crash.OccurrenceCount;
        //            }
        //        }

        //        if (recentErrors > 20) // Too many errors in 5 minutes
        //        {
        //            ErrorHandler.LogWarning($"Excessive errors detected: {recentErrors} in last 5 minutes");
        //            RealTimeMonitor.ForceOptimization();
        //        }

        //        // Check thread count
        //        var threadCount = System.Diagnostics.Process.GetCurrentProcess().Threads.Count;
        //        if (threadCount > 100)
        //        {
        //            ErrorHandler.LogWarning($"High thread count: {threadCount}");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        ErrorHandler.LogError("Health check error", ex);
        //    }
        //}

        /// <summary>
        /// Get crash statistics
        /// </summary>
        public static string GetCrashStatistics()
        {
            try
            {
                if (_crashHistory.IsEmpty)
                    return "No crashes recorded";

                var totalCrashes = 0;
                var recoverableCrashes = 0;
                var recentCrashes = 0;
                var cutoffTime = DateTime.Now.AddHours(-1);

                foreach (var crash in _crashHistory.Values)
                {
                    totalCrashes += crash.OccurrenceCount;
                    if (crash.IsRecoverable) recoverableCrashes += crash.OccurrenceCount;
                    if (crash.Timestamp > cutoffTime) recentCrashes += crash.OccurrenceCount;
                }

                return $"Crash Statistics:\n" +
                       $"  Total Crashes: {totalCrashes}\n" +
                       $"  Recoverable: {recoverableCrashes}\n" +
                       $"  Recent (1h): {recentCrashes}\n" +
                       $"  Unique Types: {_crashHistory.Count}";
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error generating crash statistics", ex);
                return "Error generating crash statistics";
            }
        }

        /// <summary>
        /// Cleanup crash protection
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                _healthCheckTimer?.Dispose();
                _healthCheckTimer = null;
                _isProtectionActive = false;

                ErrorHandler.LogInfo("Crash protection cleaned up");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Crash protection cleanup error", ex);
            }
        }

        /// <summary>
        /// Check if protection is active
        /// </summary>
        public static bool IsProtectionActive => _isProtectionActive;
    }
}
