﻿using Client;
using Guna.UI2.WinForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AotForms
{
    public partial class Form1 : Form
    {
        IntPtr mainHandle;
        private Particles1 particleSystem;
        private SolarSolution solarSolution;
        Color activeColor = Color.FromArgb(150, 255, 215, 100);
        Color defaultFillColor = Color.FromArgb(150, 255, 215, 100);
        Color originalLabelColor;
        private readonly List<ParticleSystem> _particleSystems = new List<ParticleSystem>();
        private bool _addressesScanned = false;
        private Flame _memory;
        private Bitmap colorGradientBitmap16, colorGradientBitmap15, colorGradientBitmap18, colorGradientBitmap22;
        private int selectorX16, selectorX15, selectorX18, selectorX22;
        private bool isDraggingColor16, isDraggingColor15, isDraggingColor18, isDraggingColor22;
        private Color selectedColor16, selectedColor15, selectedColor18, selectedColor22;
        private bool waitPressKey3 = false;
        private bool waitPressKey4 = false;
        private bool waitPressKey5 = false;
        public Form1(IntPtr handle)
        {
            mainHandle = handle;
            InitializeComponent();
            InitializeMemory();
            _particleSystems.Add(new ParticleSystem(guna2Panel11));
            particleSystem = new Particles1(guna2Panel2, guna2CustomCheckBox1);
            particleSystem = new Particles1(guna2Panel1, guna2CustomCheckBox2);
            particleSystem = new Particles1(guna2Panel3, guna2CustomCheckBox3);
            particleSystem = new Particles1(guna2Panel4, guna2CustomCheckBox4);
            particleSystem = new Particles1(guna2Panel5, guna2CustomCheckBox5);
            particleSystem = new Particles1(guna2Panel6, guna2CustomCheckBox6);
            particleSystem = new Particles1(guna2Panel10, guna2CustomCheckBox9);
            particleSystem = new Particles1(guna2Panel9, guna2CustomCheckBox8);
            particleSystem = new Particles1(guna2Panel17, guna2CustomCheckBox13);
            particleSystem = new Particles1(guna2Panel19, guna2CustomCheckBox15);
            particleSystem = new Particles1(guna2Panel14, guna2CustomCheckBox10);
            originalLabelColor = label2.ForeColor;
            originalLabelColor = label2.ForeColor;
            // Collega evento
            //zbbb
            hookCallback3 = new LowLevelKeyboardProc(HookCallback3);
            hookID3 = SetHook3(hookCallback3);
            hookCallback4 = new LowLevelKeyboardProc(HookCallback4);
            hookID4 = SetHook4(hookCallback4);
            hookCallback5 = new LowLevelKeyboardProc(HookCallback5);
            hookID5 = SetHook5(hookCallback5);
            guna2CustomCheckBox1.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox1, label2);
            guna2CustomCheckBox2.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox2, label1);
            InitializeGradientPanels();
            guna2CustomCheckBox3.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox3, label3);
            guna2CustomCheckBox4.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox4, label4);
            guna2CustomCheckBox5.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox5, label6);
            guna2CustomCheckBox6.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox6, label7);
            guna2CustomCheckBox8.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox8, label9);

            guna2CustomCheckBox7.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox7, label8);
            guna2CustomCheckBox9.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox9, label10);
            guna2CustomCheckBox13.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox13, label14);
            guna2CustomCheckBox15.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox15, label16);
            guna2CustomCheckBox10.CheckedChanged += (s, e) => HandleCheckChanged(guna2CustomCheckBox10, label11);
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        private static IntPtr hookID = IntPtr.Zero;
        private static IntPtr hookID2 = IntPtr.Zero;
        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);
        private void InitializeMemory()
        {
            _memory = new Flame();
            if (!_memory.SetProcess(new[] { "HD-Player" }))
            {
                MessageBox.Show("Impossibile trovare il processo HD-Player");
                return;
            }
        }
        private void HandleCheckChanged(Guna2CustomCheckBox checkBox, Label associatedLabel)
        {
            if (checkBox.Checked)
            {
                // Applica decorazioni e colori
                checkBox.ShadowDecoration.Enabled = true;
                checkBox.ShadowDecoration.Color = activeColor;
                checkBox.CheckedState.FillColor = activeColor;

                // Cambia colore etichetta
                associatedLabel.ForeColor = Color.FromArgb(150, 255, 215, 100);
            }
            else
            {
                // Ripristina tutto
                checkBox.ShadowDecoration.Enabled = false;
                checkBox.CheckedState.FillColor = defaultFillColor;

                // Ripristina colore etichetta
                associatedLabel.ForeColor = originalLabelColor;
            }
        }


        private void guna2Panel11_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2CustomCheckBox1_Click(object sender, EventArgs e)
        {
            Config.ESPLine = guna2CustomCheckBox1.Checked;

        }

        //private async void guna2CustomCheckBox7_Click(object sender, EventArgs e)
        //{
        //    var processes = Process.GetProcessesByName("HD-Player");

        //    if (processes.Length != 1)
        //    {
        //        guna2Panel8.FillColor = Color.Red;
        //        return;
        //    }

        //    var process = processes[0];
        //    var mainModulePath = Path.GetDirectoryName(process.MainModule.FileName);
        //    var adbPath = Path.Combine(mainModulePath, "HD-Adb.exe");

        //    if (!File.Exists(adbPath))
        //    {
        //        guna2Panel8.FillColor = Color.Red;
        //        return;
        //    }


        //    var adb = new Adb(adbPath);
        //    await adb.Kill();

        //    var started = await adb.Start();
        //    if (!started)
        //    {
        //        guna2Panel8.FillColor = Color.Red;
        //        return;
        //    }

        //    String pkg = "com.dts.freefireth";
        //    String lib = "libil2cpp.so";

        //    bool isFreeFireMax = false;
        //    if (isFreeFireMax)
        //    {
        //        pkg = "com.dts.freefiremax";
        //    }

        //    var moduleAddr = await adb.FindModule(pkg, lib);
        //    if (moduleAddr == 0) // If the module address is not found
        //    {
        //        guna2Panel8.FillColor = Color.Red;
        //        return;
        //    }

        //    Offsets.Il2Cpp = moduleAddr;
        //    Core.Handle = FindRenderWindow(mainHandle);

        //    var esp = new ESP();
        //    await esp.Start();

        //    new Thread(Data.Work) { IsBackground = true }.Start();
        //    new Thread(Aimbot.Work) { IsBackground = true }.Start();
        //    new Thread(Silent.Work) { IsBackground = true }.Start();


        //    guna2Panel8.FillColor = Color.LimeGreen;
        //    Console.Beep(2000, 400);
        //    guna2Panel8.Visible = false;
        //    guna2Panel7.Visible = true;




        //}
        private async void guna2CustomCheckBox7_Click(object sender, EventArgs e)
        {
            Console.WriteLine("Starting process check...");

            var processes = Process.GetProcessesByName("HD-Player");

            if (processes.Length != 1)
            {
                Console.WriteLine("Error: HD-Player process not found or multiple instances running.");
                guna2Panel8.FillColor = Color.Red;
                return;
            }

            var process = processes[0];
            var mainModulePath = Path.GetDirectoryName(process.MainModule.FileName);
            var adbPath = Path.Combine(mainModulePath, "HD-Adb.exe");

            Console.WriteLine($"ADB Path: {adbPath}");

            if (!File.Exists(adbPath))
            {
                Console.WriteLine("Error: ADB file not found.");
                guna2Panel8.FillColor = Color.Red;
                return;
            }

            var adb = new Adb(adbPath);
            await adb.Kill();

            Console.WriteLine("Attempting to start ADB...");
            var started = await adb.Start();
            if (!started)
            {
                Console.WriteLine("Error: Failed to start ADB.");
                guna2Panel8.FillColor = Color.Red;
                return;
            }

            Console.WriteLine("ADB started successfully.");

            String pkg = "com.dts.freefireth";
            String lib = "libil2cpp.so";

            bool isFreeFireMax = false;
            if (isFreeFireMax)
            {
                pkg = "com.dts.freefiremax";
            }

            var moduleAddr = await adb.FindModule(pkg, lib);
            if (moduleAddr == 0) // If the module address is not found
            {
                Console.WriteLine("Error: Module address not found.");
                guna2Panel8.FillColor = Color.Red;
                return;
            }

            Offsets.Il2Cpp = moduleAddr;
            Core.Handle = FindRenderWindow(mainHandle);

            var esp = new ESP();
            await esp.Start();

            new Thread(Data.Work) { IsBackground = true }.Start();
            new Thread(Aimbot.Work) { IsBackground = true }.Start();
            new Thread(FlyMe.Work) { IsBackground = true }.Start();
            new Thread(UpPlayer.Work) { IsBackground = true }.Start();
            new Thread(ProxTelekill.Work) { IsBackground = true }.Start();

            // تشغيل نظام التحريك التلقائي نحو العظام
            AutoTeleportToBone.Start();


            guna2Panel8.FillColor = Color.LimeGreen;
            Console.Beep(2000, 400);
            guna2Panel8.Visible = false;
            guna2Panel7.Visible = true;
            guna2Panel12.Visible = true;
            guna2Panel17.Visible = true;
            guna2Button2.Visible = true;
            guna2Button1.Visible = true;
            guna2ProgressBar1.Visible = true;
        }

        static IntPtr FindRenderWindow(IntPtr parent)
        {
            IntPtr renderWindow = IntPtr.Zero;
            WinAPI.EnumChildWindows(parent, (hWnd, lParam) =>
            {
                StringBuilder sb = new StringBuilder(256);
                WinAPI.GetWindowText(hWnd, sb, sb.Capacity);
                string windowName = sb.ToString();
                if (!string.IsNullOrEmpty(windowName))
                {
                    if (windowName != "HD-Player")
                    {
                        renderWindow = hWnd;
                    }
                }
                return true;
            }, IntPtr.Zero);

            return renderWindow;
        }
        private Point originalPanel7Position;
        private void Form1_Load(object sender, EventArgs e)
        {
            originalPanel7Position = guna2Panel7.Location;
            guna2Panel7.Visible = false;
            label5.ForeColor = Color.FromArgb(150, 255, 215, 100);
            this.guna2Panel11.MouseDown += Guna2GradientPanel11_MouseDown;
            this.guna2Panel11.MouseUp += Guna2GradientPanel11_MouseUp;
            this.guna2Panel11.MouseMove += Guna2GradientPanel11_MouseMove;
            // Colore di riempimento (lo lasciamo come l'hai impostato)
            guna2ProgressBar1.FillColor = Color.FromArgb(150, 255, 215, 100);
            guna2Panel17.Visible = false;
            // Mostra la progress bar
            guna2ProgressBar1.Visible = false;
            guna2Panel12.Visible = false;
            // ShadowDecoration con lo stesso colore ma più trasparente (ad esempio alpha 80 su 255)
            guna2ProgressBar1.ShadowDecoration.Color = Color.FromArgb(80, 255, 215, 100);
            guna2ProgressBar1.ShadowDecoration.Enabled = true;
            guna2Panel14.Visible = false;
            guna2Panel19.Visible = false;
            this.Size = new Size(416, 605);
            guna2Button2.Visible = false;
            guna2Button1.Visible = false;
        }
        private Point GeneratePoint(int a, int b)
        {
            int resultA = a + 0;
            int resultB = b * 1;
            return new Point(resultA, resultB);
        }

        private void Guna2GradientPanel11_MouseDown(object sender, MouseEventArgs e)
        {
            isDragging = true;
            lastMousePosition = e.Location;
        }

        private void Guna2GradientPanel11_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }

        private void Guna2GradientPanel11_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                var deltaX = e.X - lastMousePosition.X;
                var deltaY = e.Y - lastMousePosition.Y;
                this.Location = new Point(this.Left + deltaX, this.Top + deltaY);
            }
        }
        private bool isDragging = false;
        private Point lastMousePosition;
        private void guna2CustomCheckBox2_Click(object sender, EventArgs e)
        {
            Config.ESPBox = guna2CustomCheckBox2.Checked;
        }

        private void guna2CustomCheckBox3_Click(object sender, EventArgs e)
        {
            Config.ESPName = guna2CustomCheckBox3.Checked;
        }

        private void guna2CustomCheckBox4_Click(object sender, EventArgs e)
        {
            Config.ESPHealth = guna2CustomCheckBox4.Checked;
        }

        private void guna2CustomCheckBox5_Click(object sender, EventArgs e)
        {
            Config.ESPWeapon = guna2CustomCheckBox5.Checked;
        }

        private void guna2CustomCheckBox6_Click(object sender, EventArgs e)
        {
            Config.ESPWeaponIcon = guna2CustomCheckBox6.Checked;


        }

        private void guna2Panel9_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2CustomCheckBox8_Click(object sender, EventArgs e)
        {
            Config.AimBot = guna2CustomCheckBox8.Checked;
            //Config.proxtelekill = guna2CustomCheckBox8.Checked;
        }

        private void guna2CustomCheckBox9_Click(object sender, EventArgs e)
        {
            Config.ESPSkeleton = guna2CustomCheckBox9.Checked;
        }

        private void ApplyColor(Control ctrl, Color col)
        {
            int i = (int)Math.Sqrt(625); // inutile
            ctrl.ForeColor = col;
        }

        private void ShowBarAt(int x, int y)
        {
            guna2ProgressBar1.Location = new Point(x, y);
            guna2ProgressBar1.Visible = (true == !false); // offuscato
        }

        private void ApplyGray(params Control[] ctrls)
        {
            foreach (var ctrl in ctrls)
            {
                ctrl.ForeColor = Color.FromArgb((int)Math.Pow(4, 2) * 4, 128, 128); // 64,128,128 -> grigio
                ctrl.ForeColor = Color.Gray; // doppia riga per confondere
            }
        }

        private void HidePanels(params Panel[] panels)
        {
            foreach (var pnl in panels)
            {
                pnl.Hide();
            }
        }


        private Color DecodeColor(string encoded)
        {
            string[] parts = encoded.Split(';');
            return Color.FromArgb(int.Parse(parts[0]), int.Parse(parts[1]), int.Parse(parts[2]));
        }


        private void guna2Button1_Click(object sender, EventArgs e)
        {
            ApplyColor(guna2Button1, DecodeColor("255;215;100"));
            ShowBarAt(140, 71);
            ApplyGray(guna2Button2);

            guna2Panel12.Hide();

            // Mostra e riposiziona il panel7
            guna2Panel7.Location = originalPanel7Position;
            guna2Panel7.Show();
        }

        private void guna2Button2_Click(object sender, EventArgs e)
        {
            ApplyColor(guna2Button2, DecodeColor("255;215;100"));
            ShowBarAt(233, 71);
            ApplyGray(guna2Button1);

            guna2Panel7.Hide();

            // Mostra e posiziona il panel12
            guna2Panel12.Location = new Point(13, 132);
            guna2Panel12.Show();
        }

        private async void guna2CustomCheckBox13_Click(object sender, EventArgs e)
        {
            string encryptionKey = "7H9!kQmZp3s6v8y/";

            if (!(sender is Guna2CustomCheckBox originalCheckBox))
            {
                MessageBox.Show("Errore: il sender non è un checkbox valido.");
                return;
            }

            if (!originalCheckBox.Checked) return;

            var checkBox = DecryptObject<Guna2CustomCheckBox>(sender, encryptionKey);
            if (checkBox == null)
            {
                MessageBox.Show("Errore: il checkbox decriptato è null.");
                return;
            }

            checkBox.Enabled = false;
            checkBox.ShadowDecoration.Color = Color.FromArgb(150, 255, 215, 100);
            checkBox.ShadowDecoration.Enabled = true;
            checkBox.ShadowDecoration.Depth = 50;
            checkBox.CheckedState.FillColor = Color.FromArgb(255, 215, 100);

            try
            {
                if (!_addressesScanned)
                {
                    await AobPatchManager.InitializePatch(_memory, "Patch1",
                        "CC 3D 06 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 80 3F 33 33 13 40 00 00 B0 3F 00 00 80 3F",
                        "CC 3D 06 00 00 00 00 00 80 3f 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 33 33 13 40 00 00 B0 3F 00 00 80 3F");

                    await AobPatchManager.InitializePatch(_memory, "Patch2",
                        "B4 42 ?? 00 00 00 00 00 00 00 ?? ?? ?? 3F 00 00 80 3E 00 00 00 00 04 00 00 00 00 00 80 3F 00 00 20 41 00 00 34 42 01 00 00 00 ?? 00 00 00 00 00 00 00 00 00 00 00 00 00 80 3F ?? ?? ?? ?? ?? ?? ?? ?? 00 00 80 3F",
                        "B4 42 ?? 00 00 00 00 00 00 00 EC 51 B8 3D 8F C2 F5 3C");

                    _addressesScanned = true;
                }

                await Task.Delay(3000);

                var label1 = FindAndDecryptControl<Label>("label1", encryptionKey);
                if (label1 != null)
                {
                    label1.ForeColor = Color.FromArgb(255, 215, 100);
                }

                var panel14 = FindAndDecryptControl<Guna2Panel>("guna2Panel14", encryptionKey);
                var panel19 = FindAndDecryptControl<Guna2Panel>("guna2Panel19", encryptionKey);

                if (panel14 != null) panel14.Visible = true;
                if (panel19 != null) panel19.Visible = true;

                ExecuteEncryptedBeep(600, 250, encryptionKey);
                guna2Panel17.Visible = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Errore inatteso: {ex.Message}");
            }
            finally
            {
                checkBox.Enabled = true;

                guna2Panel17.Visible = false;
            }
        }





        #region Metodi di supporto criptati
        private T DecryptObject<T>(object input, string key) where T : class
        {
            // Implementazione semplificata - nella realtà usa una vera cifratura
            return input as T;
        }

        private T FindAndDecryptControl<T>(string name, string key) where T : class
        {
            var control = this.Controls.Find(name, true).FirstOrDefault();
            return control as T;
        }

        private void ExecuteEncryptedBeep(int frequency, int duration, string key)
        {
            // Decripta i parametri se necessario (esempio semplificato)
            int decryptedFreq = frequency; // Qui andrebbe la decriptazione vera
            int decryptedDur = duration;
            Console.Beep(decryptedFreq, decryptedDur);
        }
        #endregion
        private void HandlePatchToggle(Guna2CustomCheckBox checkBox, string patchId, Guna2CustomCheckBox otherCheckBox = null)
        {
            checkBox.Enabled = false;

            try
            {
                if (!_addressesScanned)
                {
                    MessageBox.Show("Prima esegui la scansione degli indirizzi con il checkbox 7!");
                    checkBox.Checked = false;
                    UpdateCheckBoxUI(checkBox, GetLabelFromCheckbox(checkBox));
                    return;
                }

                bool success = AobPatchManager.TogglePatch(_memory, patchId, checkBox.Checked);

                if (!success)
                {
                    checkBox.Checked = !checkBox.Checked;
                    MessageBox.Show($"Errore durante l'operazione su {patchId}");
                }
            }
            finally
            {
                checkBox.Enabled = true;
                UpdateCheckBoxUI(checkBox, GetLabelFromCheckbox(checkBox));
            }
        }

        private void UpdateCheckBoxUI(Guna2CustomCheckBox checkBox, Label label)
        {
            if (checkBox.Checked)
            {
                checkBox.ShadowDecoration.Color = Color.FromArgb(150, 255, 215, 100);
                checkBox.ShadowDecoration.Enabled = true;
                checkBox.ShadowDecoration.Depth = 50;
                checkBox.CheckedState.FillColor = Color.FromArgb(255, 215, 100);
                if (label != null)
                    label.ForeColor = Color.FromArgb(255, 215, 100);
            }
            else
            {
                checkBox.ShadowDecoration.Enabled = false;
                checkBox.CheckedState.FillColor = Color.FromArgb(255, 215, 100);
                if (label != null)
                    label.ForeColor = Color.Gray;
            }
        }
        private Label GetLabelFromCheckbox(Guna2CustomCheckBox checkBox)
        {
            if (checkBox == guna2CustomCheckBox15) return label2;
            if (checkBox == guna2CustomCheckBox10) return label3;
            return null;
        }
        private void guna2CustomCheckBox15_Click(object sender, EventArgs e)
        {
            HandlePatchToggle(guna2CustomCheckBox15, "Patch1", guna2CustomCheckBox10);
        }



        private async void guna2CustomCheckBox10_Click(object sender, EventArgs e)
        {
            HandlePatchToggle(guna2CustomCheckBox10, "Patch2", guna2CustomCheckBox15);
        }
        private void InitializeGradientPanels()
        {
            // Inizializza i gradienti per tutti i panel
            CreaGradiente16();
            CreaGradiente15();
            CreaGradiente18();
            CreaGradiente22();

            // Collega gli eventi per ogni panel
            guna2Panel16.Paint += Guna2Panel16_Paint;
            guna2Panel16.MouseDown += Guna2Panel16_MouseDown;
            guna2Panel16.MouseMove += Guna2Panel16_MouseMove;
            guna2Panel16.MouseUp += Guna2Panel16_MouseUp;

            guna2Panel15.Paint += Guna2Panel15_Paint;
            guna2Panel15.MouseDown += Guna2Panel15_MouseDown;
            guna2Panel15.MouseMove += Guna2Panel15_MouseMove;
            guna2Panel15.MouseUp += Guna2Panel15_MouseUp;

            guna2Panel18.Paint += Guna2Panel18_Paint;
            guna2Panel18.MouseDown += Guna2Panel18_MouseDown;
            guna2Panel18.MouseMove += Guna2Panel18_MouseMove;
            guna2Panel18.MouseUp += Guna2Panel18_MouseUp;

            guna2Panel22.Paint += Guna2Panel22_Paint;
            guna2Panel22.MouseDown += Guna2Panel22_MouseDown;
            guna2Panel22.MouseMove += Guna2Panel22_MouseMove;
            guna2Panel22.MouseUp += Guna2Panel22_MouseUp;
        }



        private void Guna2Panel16_Paint(object sender, PaintEventArgs e)
        {
            DrawSelector(e.Graphics, selectorX16, guna2Panel16.Height / 2);
        }

        private void Guna2Panel16_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingColor16 = true;
            UpdateColor16(e.X);
        }

        private void Guna2Panel16_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingColor16 && e.X >= 0 && e.X < guna2Panel16.Width)
            {
                UpdateColor16(e.X);
            }
        }

        private void Guna2Panel16_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingColor16 = false;
        }

        private void UpdateColor16(int x)
        {
            selectorX16 = x;
            if (colorGradientBitmap16 != null)
            {
                selectorX16 = Math.Max(0, Math.Min(selectorX16, colorGradientBitmap16.Width - 1));
                selectedColor16 = colorGradientBitmap16.GetPixel(selectorX16, guna2Panel16.Height / 2);
                // Usa selectedColor16 come necessario
                guna2Panel16.Invalidate();
                Config.ESPSkeletonColor = selectedColor16;

            }
        }



        private void Guna2Panel15_Paint(object sender, PaintEventArgs e)
        {
            DrawSelector(e.Graphics, selectorX15, guna2Panel15.Height / 2);
        }

        private void Guna2Panel15_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingColor15 = true;
            UpdateColor15(e.X);
        }

        private void Guna2Panel15_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingColor15 && e.X >= 0 && e.X < guna2Panel15.Width)
            {
                UpdateColor15(e.X);
            }
        }

        private void Guna2Panel15_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingColor15 = false;
        }

        private void UpdateColor15(int x)
        {
            selectorX15 = x;
            if (colorGradientBitmap15 != null)
            {
                selectorX15 = Math.Max(0, Math.Min(selectorX15, colorGradientBitmap15.Width - 1));
                selectedColor15 = colorGradientBitmap15.GetPixel(selectorX15, guna2Panel15.Height / 2);
                // Usa selectedColor15 come necessario
                guna2Panel15.Invalidate();
                Config.ESPBoxColor = selectedColor15;
            }
        }



        private void Guna2Panel18_Paint(object sender, PaintEventArgs e)
        {
            DrawSelector(e.Graphics, selectorX18, guna2Panel18.Height / 2);
        }

        private void Guna2Panel18_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingColor18 = true;
            UpdateColor18(e.X);
        }

        private void Guna2Panel18_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingColor18 && e.X >= 0 && e.X < guna2Panel18.Width)
            {
                UpdateColor18(e.X);
            }
        }

        private void Guna2Panel18_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingColor18 = false;
        }

        private void UpdateColor18(int x)
        {
            selectorX18 = x;
            if (colorGradientBitmap18 != null)
            {
                selectorX18 = Math.Max(0, Math.Min(selectorX18, colorGradientBitmap18.Width - 1));
                selectedColor18 = colorGradientBitmap18.GetPixel(selectorX18, guna2Panel18.Height / 2);
                // Usa selectedColor18 come necessario
                guna2Panel18.Invalidate();
                Config.ESPNameColor = selectedColor18;
            }
        }

        private void CreaGradiente15()
        {
            colorGradientBitmap15 = new Bitmap(guna2Panel15.Width, guna2Panel15.Height);
            using (Graphics g = Graphics.FromImage(colorGradientBitmap15))
            {
                Rectangle rect = new Rectangle(0, 0, colorGradientBitmap15.Width, colorGradientBitmap15.Height);
                using (LinearGradientBrush brush = new LinearGradientBrush(rect, Color.DarkRed, Color.Yellow, LinearGradientMode.Horizontal))
                {
                    ColorBlend colorBlend = new ColorBlend
                    {
                        Colors = new Color[] {
                    Color.DarkRed,
                    Color.Crimson,
                    Color.Red,
                    Color.OrangeRed,
                    Color.Orange,
                    Color.Gold,
                    Color.Yellow
                },
                        Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                    };
                    brush.InterpolationColors = colorBlend;
                    g.FillRectangle(brush, rect);
                }
            }
            guna2Panel15.BackgroundImage = colorGradientBitmap15;
            selectorX15 = guna2Panel15.Width - 20;
            guna2Panel15.Invalidate();
        }

        private void CreaGradiente18()
        {
            colorGradientBitmap18 = new Bitmap(guna2Panel18.Width, guna2Panel18.Height);
            using (Graphics g = Graphics.FromImage(colorGradientBitmap18))
            {
                Rectangle rect = new Rectangle(0, 0, colorGradientBitmap18.Width, colorGradientBitmap18.Height);
                using (LinearGradientBrush brush = new LinearGradientBrush(rect, Color.DarkRed, Color.Yellow, LinearGradientMode.Horizontal))
                {
                    ColorBlend colorBlend = new ColorBlend
                    {
                        Colors = new Color[] {
                    Color.DarkRed,
                    Color.Crimson,
                    Color.Red,
                    Color.OrangeRed,
                    Color.Orange,
                    Color.Gold,
                    Color.Yellow
                },
                        Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                    };
                    brush.InterpolationColors = colorBlend;
                    g.FillRectangle(brush, rect);
                }
            }
            guna2Panel18.BackgroundImage = colorGradientBitmap18;
            selectorX18 = guna2Panel18.Width - 20;
            guna2Panel18.Invalidate();
        }

        private void CreaGradiente22()
        {
            colorGradientBitmap22 = new Bitmap(guna2Panel22.Width, guna2Panel22.Height);
            using (Graphics g = Graphics.FromImage(colorGradientBitmap22))
            {
                Rectangle rect = new Rectangle(0, 0, colorGradientBitmap22.Width, colorGradientBitmap22.Height);
                using (LinearGradientBrush brush = new LinearGradientBrush(rect, Color.DarkRed, Color.Yellow, LinearGradientMode.Horizontal))
                {
                    ColorBlend colorBlend = new ColorBlend
                    {
                        Colors = new Color[] {
                    Color.DarkRed,
                    Color.Crimson,
                    Color.Red,
                    Color.OrangeRed,
                    Color.Orange,
                    Color.Gold,
                    Color.Yellow
                },
                        Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                    };
                    brush.InterpolationColors = colorBlend;
                    g.FillRectangle(brush, rect);
                }
            }
            guna2Panel22.BackgroundImage = colorGradientBitmap22;
            selectorX22 = guna2Panel22.Width - 20;
            guna2Panel22.Invalidate();
        }

        private void CreaGradiente16()
        {
            colorGradientBitmap16 = new Bitmap(guna2Panel16.Width, guna2Panel16.Height);
            using (Graphics g = Graphics.FromImage(colorGradientBitmap16))
            {
                Rectangle rect = new Rectangle(0, 0, colorGradientBitmap16.Width, colorGradientBitmap16.Height);
                using (LinearGradientBrush brush = new LinearGradientBrush(rect, Color.DarkRed, Color.Yellow, LinearGradientMode.Horizontal))
                {
                    ColorBlend colorBlend = new ColorBlend
                    {
                        Colors = new Color[] {
                    Color.DarkRed,
                    Color.Crimson,
                    Color.Red,
                    Color.OrangeRed,
                    Color.Orange,
                    Color.Gold,
                    Color.Yellow
                },
                        Positions = new float[] { 0.0f, 0.16f, 0.32f, 0.48f, 0.64f, 0.80f, 1.0f }
                    };
                    brush.InterpolationColors = colorBlend;
                    g.FillRectangle(brush, rect);
                }
            }
            guna2Panel16.BackgroundImage = colorGradientBitmap16;
            selectorX16 = guna2Panel16.Width - 20;
            guna2Panel16.Invalidate();
        }

        private void Guna2Panel22_Paint(object sender, PaintEventArgs e)
        {
            DrawSelector(e.Graphics, selectorX22, guna2Panel22.Height / 2);
        }

        private void Guna2Panel22_MouseDown(object sender, MouseEventArgs e)
        {
            isDraggingColor22 = true;
            UpdateColor22(e.X);
        }

        private void Guna2Panel22_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDraggingColor22 && e.X >= 0 && e.X < guna2Panel22.Width)
            {
                UpdateColor22(e.X);
            }
        }

        private void Guna2Panel22_MouseUp(object sender, MouseEventArgs e)
        {
            isDraggingColor22 = false;
        }

        private void UpdateColor22(int x)
        {
            selectorX22 = x;
            if (colorGradientBitmap22 != null)
            {
                selectorX22 = Math.Max(0, Math.Min(selectorX22, colorGradientBitmap22.Width - 1));
                selectedColor22 = colorGradientBitmap22.GetPixel(selectorX22, guna2Panel22.Height / 2);
                // Usa selectedColor22 come necessario
                guna2Panel22.Invalidate();
                // Aggiorna il colore in Config.ESPLine
                Config.ESPLineColor = selectedColor22;
            }
        }

        // Metodo comune per disegnare il selettore
        private void DrawSelector(Graphics g, int x, int y)
        {
            int circleSize = 10;
            using (Pen borderPen = new Pen(Color.Black, 1))
            using (SolidBrush brush = new SolidBrush(Color.White))
            {
                g.FillEllipse(brush, x - circleSize / 2, y - circleSize / 2, circleSize, circleSize);
                g.DrawEllipse(borderPen, x - circleSize / 2, y - circleSize / 2, circleSize, circleSize);
            }
        }

        private void guna2CustomCheckBox11_CheckedChanged(object sender, EventArgs e)
        {
            Config.flyme = guna2CustomCheckBox11.Checked;
        }

        private void guna2CustomCheckBox14_CheckedChanged_AutoTeleport(object sender, EventArgs e)
        {
            Config.AutoTeleportToBone = guna2CustomCheckBox14.Checked;
            Config.Notif();
        }

        private IntPtr SetHook3(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                IntPtr moduleHandle = curModule.BaseAddress; // Ottieni l'indirizzo del modulo
                return SetWindowsHookEx(WH_KEYBOARD_LL3, proc, moduleHandle, 0);
            }
        }
        private const int WH_KEYBOARD_LL3 = 13;
        private const int WM_KEYDOWN3 = 0x0100;
        private IntPtr HookCallback3(int nCode3, IntPtr wParam3, IntPtr lParam3)
        {
            if (nCode3 >= 0 && wParam3 == (IntPtr)WM_KEYDOWN3)
            {
                Keys key = (Keys)Marshal.ReadInt32(lParam3);
                KeysConverter keyConverter = new KeysConverter();
                string keyName = keyConverter.ConvertToString(key);

                // Se stai aspettando che l'utente prema un tasto per settare il binding
                if (waitPressKey3)
                {
                    if (keyName.Equals("Escape"))
                    {
                        guna2Button5.Text = "None";
                    }
                    else
                    {
                        guna2Button5.Text = keyName;
                    }
                    waitPressKey3 = false;
                }
                else
                {
                    string buttonText = guna2Button5.Text.Replace("...", "").Trim();
                    if (!string.IsNullOrEmpty(buttonText) &&
                        Enum.TryParse(buttonText, out Keys binding) && binding != Keys.None)
                    {
                        if (key == binding)
                        {
                            // Inverti il valore di Config.flyme
                            Config.flyme = !Config.flyme;

                            // Aggiorna la checkbox per riflettere il nuovo stato
                            guna2CustomCheckBox11.Checked = Config.flyme;
                        }
                    }
                }
            }

            return CallNextHookEx(hookID3, nCode3, wParam3, lParam3);
        }

        IntPtr hookID3 = IntPtr.Zero;
        private LowLevelKeyboardProc hookCallback3;
        IntPtr hookID4 = IntPtr.Zero;
        private LowLevelKeyboardProc hookCallback4;
        IntPtr hookID5 = IntPtr.Zero;
        private LowLevelKeyboardProc hookCallback5;
        private void guna2Button5_Click(object sender, EventArgs e)
        {
            waitPressKey3 = true;
            // Chiama il metodo per attendere un tasto
            guna2Button5.Text = "..."; // Indica che si sta aspettando un tasto

        }

        private void guna2CustomCheckBox12_Click(object sender, EventArgs e)
        {

        }

        private void guna2Button3_Click(object sender, EventArgs e)
        {

        }

        private void guna2Button3_Click_1(object sender, EventArgs e)
        {

        }

        private void guna2CustomCheckBox14_CheckedChanged(object sender, EventArgs e)
        {
            Config.proxtelekill = guna2CustomCheckBox14.Checked;

        }

        private IntPtr SetHook5(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                IntPtr moduleHandle = curModule.BaseAddress; // Ottieni l'indirizzo del modulo
                return SetWindowsHookEx(WH_KEYBOARD_LL5, proc, moduleHandle, 0);
            }
        }
        private const int WH_KEYBOARD_LL5 = 13;
        private const int WM_KEYDOWN5 = 0x0100;
        private IntPtr HookCallback5(int nCode3, IntPtr wParam3, IntPtr lParam3)
        {
            if (nCode3 >= 0 && wParam3 == (IntPtr)WM_KEYDOWN5)
            {
                Keys key = (Keys)Marshal.ReadInt32(lParam3);
                KeysConverter keyConverter = new KeysConverter();
                string keyName = keyConverter.ConvertToString(key);

                // Se stai aspettando che l'utente prema un tasto per settare il binding
                if (waitPressKey5)
                {
                    if (keyName.Equals("Escape"))
                    {
                        guna2Button3.Text = "None";
                    }
                    else
                    {
                        guna2Button3.Text = keyName;
                    }
                    waitPressKey5 = false;
                }
                else
                {
                    string buttonText = guna2Button3.Text.Replace("...", "").Trim();
                    if (!string.IsNullOrEmpty(buttonText) &&
                        Enum.TryParse(buttonText, out Keys binding) && binding != Keys.None)
                    {
                        if (key == binding)
                        {
                            // Inverti il valore di Config.flyme
                            Config.proxtelekill = !Config.proxtelekill;

                            // Aggiorna la checkbox per riflettere il nuovo stato
                            guna2CustomCheckBox12.Checked = Config.proxtelekill;
                        }
                    }
                }
            }

            return CallNextHookEx(hookID5, nCode3, wParam3, lParam3);
        }

        private void guna2Button3_Click_2(object sender, EventArgs e)
        {
            waitPressKey5 = true;
            // Chiama il metodo per attendere un tasto
            guna2Button3.Text = "..."; // Indica che si sta aspettando un tasto
        }
        private IntPtr SetHook4(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                IntPtr moduleHandle = curModule.BaseAddress; // Ottieni l'indirizzo del modulo
                return SetWindowsHookEx(WH_KEYBOARD_LL4, proc, moduleHandle, 0);
            }
        }
        private const int WH_KEYBOARD_LL4 = 13;
        private const int WM_KEYDOWN4 = 0x0100;
        private IntPtr HookCallback4(int nCode3, IntPtr wParam3, IntPtr lParam3)
        {
            if (nCode3 >= 0 && wParam3 == (IntPtr)WM_KEYDOWN4)
            {
                Keys key = (Keys)Marshal.ReadInt32(lParam3);
                KeysConverter keyConverter = new KeysConverter();
                string keyName = keyConverter.ConvertToString(key);

                // Se stai aspettando che l'utente prema un tasto per settare il binding
                if (waitPressKey4)
                {
                    if (keyName.Equals("Escape"))
                    {
                        guna2Button4.Text = "None";
                    }
                    else
                    {
                        guna2Button4.Text = keyName;
                    }
                    waitPressKey4 = false;
                }
                else
                {
                    string buttonText = guna2Button4.Text.Replace("...", "").Trim();
                    if (!string.IsNullOrEmpty(buttonText) &&
                        Enum.TryParse(buttonText, out Keys binding) && binding != Keys.None)
                    {
                        if (key == binding)
                        {
                            // Inverti il valore di Config.flyme
                            Config.UpPlayer = !Config.UpPlayer;

                            // Aggiorna la checkbox per riflettere il nuovo stato
                            guna2CustomCheckBox12.Checked = Config.UpPlayer;
                        }
                    }
                }
            }

            return CallNextHookEx(hookID4, nCode3, wParam3, lParam3);
        }
        private void guna2Button4_Click(object sender, EventArgs e)
        {
            waitPressKey4 = true;
            // Chiama il metodo per attendere un tasto
            guna2Button4.Text = "..."; // Indica che si sta aspettando un tasto
        }

        private void guna2CustomCheckBox12_CheckedChanged(object sender, EventArgs e)
        {
            Config.UpPlayer = guna2CustomCheckBox12.Checked;
        }

        private void guna2Panel14_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2Panel17_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2Panel21_Paint(object sender, PaintEventArgs e)
        {

        }

        private void guna2Button6_Click(object sender, EventArgs e)
        {
            // فتح نافذة إعدادات التحريك التلقائي نحو العظام
            AutoTeleportManager.ShowConfigWindow();
            Config.Notif();
        }
    }
}


